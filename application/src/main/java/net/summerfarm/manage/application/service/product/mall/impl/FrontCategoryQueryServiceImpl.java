package net.summerfarm.manage.application.service.product.mall.impl;


import net.summerfarm.manage.application.inbound.controller.product.assembler.FrontCategoryAssembler;
import net.summerfarm.manage.application.inbound.controller.product.vo.FrontCategoryVO;
import net.summerfarm.manage.application.service.product.mall.FrontCategoryQueryService;
import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import net.summerfarm.manage.domain.product.repository.FrontCategoryQueryRepository;
import net.xianmu.common.cache.InMemoryCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
*
* <AUTHOR>
* @date 2025-03-27 15:26:47
* @version 1.0
*
*/
@Service
public class FrontCategoryQueryServiceImpl implements FrontCategoryQueryService {

    @Autowired
    private FrontCategoryQueryRepository frontCategoryQueryRepository;

    @Override
    @InMemoryCache(expiryTimeInSeconds = 3 * 60)
    public List<FrontCategoryVO> getPopTree() {
        List<FrontCategoryEntity> frontCategoryEntities = frontCategoryQueryRepository.selectAllPopCategory();
        return buildTreeStructure(frontCategoryEntities);
    }


    public List<FrontCategoryVO> buildTreeStructure(List<FrontCategoryEntity> entityList) {
        // 转换为VO列表，假设已处理subFrontCategoryList初始化
        List<FrontCategoryVO> voList = FrontCategoryAssembler.INSTANCE.toFrontCategoryVOList(entityList);
        Map<Integer, FrontCategoryVO> nodeMap = new HashMap<>();
        List<FrontCategoryVO> roots = new ArrayList<>();

        // 第一次遍历，填充哈希表并收集根节点
        for (FrontCategoryVO vo : voList) {
            nodeMap.put(vo.getId(), vo);
            if (vo.getParentId() == null) {
                roots.add(vo);
            }
        }

        // 第二次遍历，构建父子关系
        for (FrontCategoryVO vo : voList) {
            Integer parentId = vo.getParentId();
            if (parentId != null) {
                FrontCategoryVO parent = nodeMap.get(parentId);
                if (parent != null) {
                    // 确保子列表已初始化
                    if (parent.getSubFrontCategoryList() == null) {
                        parent.setSubFrontCategoryList(new ArrayList<>());
                    }
                    parent.getSubFrontCategoryList().add(vo);
                }
            }
        }
        return roots;
    }
}