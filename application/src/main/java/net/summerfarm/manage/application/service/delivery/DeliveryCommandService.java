package net.summerfarm.manage.application.service.delivery;

/**
 * @ClassName DeliveryCommandService
 * @Description
 * <AUTHOR>
 * @Date 14:48 2024/1/30
 * @Version 1.0
 **/
public interface DeliveryCommandService {

    /***
     * @author: lzh
     * @description: 订单配送提醒通知
     * @date: 2024/1/30 14:50
     * @param: [orderNo]
     * @return: void
    */
    void orderDeliveryNotice(String orderNo);

    /***
     * @author: lzh
     * @description: 省心送配送计划提醒通知
     * @date: 2024/2/23 14:08
     * @param: [orderNo]
     * @return: void
     **/
    void timingDeliveryNotice(String orderNo);
}
