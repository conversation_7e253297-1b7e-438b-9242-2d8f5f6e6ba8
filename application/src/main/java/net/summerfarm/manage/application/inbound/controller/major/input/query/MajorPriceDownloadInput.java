package net.summerfarm.manage.application.inbound.controller.major.input.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.manage.domain.major.valueobject.MajorPriceValueObject;
import net.xianmu.common.input.BasePageInput;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class MajorPriceDownloadInput extends BasePageInput {
    /**
     * adminId
     */
    @NotNull(message = "品牌id不能为空")
    private Integer adminId;

    /**
     * 1 账期 2  现结
     */
    @NotNull(message = "direct不能为空")
    private Integer direct;

    @Size(min = 1, max = 200, message = "最多支持导出200条")
    @NotNull(message = "sku和运营大区不能为空")
    List<MajorPriceValueObject> largeNoAndSkuList;

    /**
     * 报价单状态：0: 未生效， 1：已生效， 2：失效,3:待提交
     */
    @NotNull(message = "validStatus不能为空")
    private Integer validStatus;

}
