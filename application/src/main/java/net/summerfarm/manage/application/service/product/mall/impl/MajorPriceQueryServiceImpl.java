package net.summerfarm.manage.application.service.product.mall.impl;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceDownloadInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceRepeatQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceLargeAreaVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.SkuAreaWarehouseNoMapVO;
import net.summerfarm.manage.application.service.product.converter.MajorPriceConvert;
import net.summerfarm.manage.application.service.product.mall.AreaSkuQueryService;
import net.summerfarm.manage.application.service.product.mall.MajorPriceQueryService;
import net.summerfarm.manage.application.util.QuotationExporter;
import net.summerfarm.manage.application.util.UserInfoHolder;
import net.summerfarm.manage.common.constants.AppConsts;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.common.enums.EditTypeEnum;
import net.summerfarm.manage.common.enums.MajorPricePageStatusEnum;
import net.summerfarm.manage.common.enums.MajorPriceStatusEnum;
import net.summerfarm.manage.common.enums.download.FileDownloadTypeEnum;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.area.entity.LargeArea;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.major.entity.MajorRebateEntity;
import net.summerfarm.manage.domain.major.repository.MajorRebateQueryRepository;
import net.summerfarm.manage.domain.major.utils.PriceCalculator;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceVO;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceFlatObject;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceItemFlatObject;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.repository.MajorPriceQueryRepository;
import net.summerfarm.manage.domain.product.repository.ProductsPropertyValueQueryRepository;
import net.summerfarm.manage.facade.goods.ProductFacade;
import net.summerfarm.manage.facade.inventory.ProductCostQueryFacade;
import net.summerfarm.manage.facade.inventory.SaleInventoryCenterQueryFacade;
import net.summerfarm.manage.facade.inventory.dto.ProductCostQueryDto;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/2/21 14:27
 */

@Service
@Slf4j
public class MajorPriceQueryServiceImpl implements MajorPriceQueryService {

    @Resource
    private AdminQueryRepository adminQueryRepository;

    @Resource
    private MajorPriceQueryRepository majorPriceQueryRepository;

    @Resource
    private AreaSkuQueryService areaSkuQueryService;

    @Resource
    private AreaQueryRepository areaQueryRepository;

    @Resource
    private ProductFacade productFacade;

    @Resource
    private ProductCostQueryFacade productCostQueryFacade;

    @Resource
    private SaleInventoryCenterQueryFacade saleInventoryCenterQueryFacade;
    @Resource
    private MajorRebateQueryRepository majorRebateQueryRepository;

    @Resource
    private ProductsPropertyValueQueryRepository propertyValueQueryRepository;

    @Override
    public PageInfo<MajorPriceLargeAreaVO> majorPricePage(MajorPriceQueryInput majorPriceInput) {
        Integer validStatus = majorPriceInput.getValidStatus();
        if(MajorPricePageStatusEnum.INVALID.getType().equals(validStatus)) {
            log.info("状态参数不可为：已失效");
            throw new ParamsException("参数异常");
        }

        // 先查询大区维度分页
        PageInfo<MajorPriceFlatObject> flatObjectPageInfo = majorPriceQueryRepository.selectMajorPricePage(MajorPriceConvert.INSTANCE.input2Param(majorPriceInput));
        PageInfo<MajorPriceLargeAreaVO> pageInfo = PageInfoConverter.toPageResp(flatObjectPageInfo, MajorPriceConvert.INSTANCE::object2Vo);
        if(CollectionUtil.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }

        // 补充成本 库存信息
        List<MajorPriceLargeAreaVO> list = pageInfo.getList();
        List<MajorPriceVO> cityList = new ArrayList<>();
        list.stream().filter(vos -> CollectionUtil.isNotEmpty(vos.getMajorPrices())).forEach(vos -> cityList.addAll(vos.getMajorPrices()));
        // 补充大客户价格策略
        this.wrapPriceCityListForMajorRebate(cityList, Long.valueOf(majorPriceInput.getAdminId()), majorPriceInput.getDirect());
        this.wrapPriceCityList(cityList);
        return pageInfo;
    }

    @Override
    public PageInfo<MajorPriceVO> majorPriceCityPage(MajorPriceQueryInput majorPriceInput) {
        // 查询城市维度的数据
        PageInfo<MajorPriceItemFlatObject> flatObjectPageInfo = majorPriceQueryRepository.selectMajorPriceCityPage(MajorPriceConvert.INSTANCE.input2Param(majorPriceInput));
        PageInfo<MajorPriceVO> pageInfo = PageInfoConverter.toPageResp(flatObjectPageInfo, MajorPriceConvert.INSTANCE::object2Vo);

        if(CollectionUtil.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }


        // 补充大客户价格策略
        this.wrapPriceCityListForMajorRebate(pageInfo.getList(), Long.valueOf(majorPriceInput.getAdminId()), majorPriceInput.getDirect());
        // 补充成本、库存信息
        this.wrapPriceCityList(pageInfo.getList());
        return pageInfo;
    }

    @Override
    public Long majorPriceDownload(MajorPriceDownloadInput input) {
        Integer adminId = input.getAdminId();
        AdminEntity adminEntity = adminQueryRepository.selectByPrimaryKey(Long.valueOf(adminId));

        if(adminEntity == null) {
            log.error("大客户不存在!adminId:{}", adminId);
            throw new BizException("大客户不存在!");
        }
        String nameRemakes = StringUtils.isNotBlank(adminEntity.getNameRemakes()) ? adminEntity.getNameRemakes(): "鲜沐农场";
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd") + String.format("%s-报价函.xlsx", nameRemakes);
        DownloadCenterRecordDTO record = new DownloadCenterRecordDTO();
        record.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        record.setUserId(UserInfoHolder.getAdminId());
        record.setBizType(FileDownloadTypeEnum.XIANMU_MAJOR_PRICE_DOWNLOAD.getType());
        record.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        record.setFileName(fileName);
        return DownloadCenterHelper.build(record).asyncWriteWithOssResp(record, p -> {
            try {
                 String ossKey = QuotationExporter.export(
                         fileName,
                        () -> majorPriceQueryRepository.selectMajorPriceDownloadList(MajorPriceConvert.INSTANCE.input2Param(input)), // 实现数据获取逻辑
                        nameRemakes + Global.XIANMU_MAJOR_PRICE_DOWNLOAD,
                        UserInfoHolder.getAdminName(),
                        LocalDate.now().format(DateTimeFormatter.ISO_DATE)
                );

                // 返回文件地址
                DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
                downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
                downloadCenterOssRespDTO.setOssBucketKey(ossKey);
                return downloadCenterOssRespDTO;
            } catch (Exception e) {
                log.error("导出大客户报价单异常！", e);
                throw new BizException("导出大客户报价单失败，请稍后再试");
            }
        });
    }

    private void wrapPriceCityListForMajorRebate(List<MajorPriceVO> pageInfoList, Long adminId, Integer direct){
        if(direct == null || direct != 2) {
            return;
        }

        // sku维度
        Map<String, Set<Integer>> skuAreaNoMap = pageInfoList.stream().collect(Collectors.groupingBy(MajorPriceVO::getSku, Collectors.mapping(MajorPriceVO::getAreaNo, Collectors.toSet())));
        List<MajorRebateEntity> skuMajorRebates = new ArrayList<>();
        skuAreaNoMap.forEach((k, v) -> skuMajorRebates.addAll(majorRebateQueryRepository.selectListByCate(adminId.intValue(), new ArrayList<>(v), 2, k)));
        Map<String, MajorRebateEntity> skuMajorRebateMap = skuMajorRebates.stream().collect(Collectors.toMap(e -> e.getSku() + e.getAreaNo(), Function.identity()));

        // 类目维度
        List<MajorRebateEntity> cateMajorRebates = new ArrayList<>();
        Map<Long, Set<Integer>> cateAreaNoMap = pageInfoList.stream().collect(Collectors.groupingBy(MajorPriceVO::getCategoryId, Collectors.mapping(MajorPriceVO::getAreaNo, Collectors.toSet())));
        cateAreaNoMap.forEach((k, v) -> cateMajorRebates.addAll(majorRebateQueryRepository.selectListByCate(adminId.intValue(), new ArrayList<>(v), 1, String.valueOf(k))));
        Map<String, MajorRebateEntity> cateMajorRebateMap = cateMajorRebates.stream().collect(Collectors.toMap(e -> e.getSku() + e.getAreaNo(), Function.identity()));

        pageInfoList.forEach(item -> {
            String skuKey = item.getSku() + item.getAreaNo();
            String cateKey = String.valueOf(item.getCategoryId()) + item.getAreaNo();
            MajorRebateEntity rebateEntity = skuMajorRebateMap.get(skuKey);
            if(rebateEntity == null) {
                rebateEntity = cateMajorRebateMap.get(cateKey);
            }
            if(rebateEntity != null) {
                item.setMajorRebateNumber(BigDecimal.valueOf(rebateEntity.getNumber()));
                item.setMajorRebateType(rebateEntity.getType());
            }
        });
    }

    private void wrapPriceCityList(List<MajorPriceVO> pageInfoList){
        // 1.查询成本信息
        List<ProductCostQueryDto> queryDtos = pageInfoList.stream().filter(item -> StringUtils.isNotBlank(item.getSku())).filter(item -> null != item.getWarehouseNo()).map(MajorPriceConvert.INSTANCE::vo2ProductCostQueryDto).collect(Collectors.toList());
        Map<String, ProductCostQueryResp> productCostMap = productCostQueryFacade.selectMapBySkuAndWarehouseNosWithLastRecord(queryDtos);

        // 2.查询库存信息
        Map<String, Set<Integer>> collect = pageInfoList.stream().filter(item -> StringUtils.isNotBlank(item.getSku())).filter(item -> null != item.getWarehouseNo())
                .collect(Collectors.groupingBy(
                        MajorPriceVO::getSku,
                        Collectors.mapping(
                                MajorPriceVO::getWarehouseNo,
                                Collectors.toSet()
                        )
                ));
        List<WarehouseSkuInventoryDetailResDTO> skuInventoryDetailResDTOS = saleInventoryCenterQueryFacade.queryWarehouseSkuInventoryList(collect);
        Map<String, Integer> storeQuantityMap = skuInventoryDetailResDTOS.stream()
                .collect(Collectors.toMap(
                        item -> item.getWarehouseNo() + "|" + item.getSkuCode(),
                        WarehouseSkuInventoryDetailResDTO::getOnlineQuantity
                ));

        // 3.查询销售属性
        List<Long> pdIds = pageInfoList.stream().map(MajorPriceVO::getPdId).distinct().collect(Collectors.toList());
        List<Integer> salePropertyIds = new ArrayList<>();
        salePropertyIds.add(AppConsts.ORIGIN);
        List<ProductsPropertyValueEntity> valueEntityList = propertyValueQueryRepository.listByPdIds(pdIds, salePropertyIds);
        Map<Long, ProductsPropertyValueEntity> propertyMap = valueEntityList.stream()
                .collect(Collectors.toMap(ProductsPropertyValueEntity::getPdId, Function.identity()));

        // 补充成本、库存、产地信息
        pageInfoList.forEach(item -> {
            String compositeKey = item.getWarehouseNo() + "|" + item.getSku();
            ProductCostQueryResp resp = productCostMap.getOrDefault(compositeKey, null);
            if(resp != null) {
                // 成本
                item.setCost(resp.getCurrentCost());
            }
            // 库存
            item.setStoreQuantity(storeQuantityMap.get(compositeKey));
            // 实际价格
            BigDecimal price = PriceCalculator.calculateMajorPriceByType(item.getPrice(), item.getSalePrice(), item.getPriceAdjustmentValue(), item.getPriceType());
            item.setPrice(price);
            // 产地
            ProductsPropertyValueEntity productsPropertyValueEntity = propertyMap.get(item.getPdId());
            if(productsPropertyValueEntity != null) {
                item.setOrigin(productsPropertyValueEntity.getProductsPropertyValue());
            }
        });
    }


    private static boolean isOverlap(LocalDateTime start1, LocalDateTime end1, LocalDateTime start2, LocalDateTime end2) {
        return (start1.isBefore(end2) || start1.isEqual(end2)) && (end1.isAfter(start2) || end1.isEqual(start2));
    }
    @Override
    public String repeatQuery(MajorPriceRepeatQueryInput input) {
        LocalDateTime validTime = input.getValidTime();
        LocalDateTime invalidTime = input.getInvalidTime();
        Integer direct = input.getDirect();
        Integer adminId = input.getAdminId();

        if(input.getEditType ().equals (EditTypeEnum.NEW.getCode ())){
            List<MajorPriceInput> inputs = input.getInputs ();
            List<Integer> largeAreaNos = inputs.stream ().map (MajorPriceInput::getLargeAreaNo).collect (Collectors.toList ());
            if(CollectionUtils.isEmpty (largeAreaNos)){
                throw new BizException ("运营大区不能为空");
            }
            List<LargeArea> largeAreas = areaQueryRepository.queryLargeAreaByLargeAreaNos (largeAreaNos);
            Map<Integer, String> largeAreaMap = largeAreas.stream().collect(Collectors.toMap(LargeArea::getLargeAreaNo,LargeArea::getLargeAreaName));

            SkuAreaWarehouseNoMapVO skuAreaWarehouseNoMapVO = areaSkuQueryService.queryAvailableAreaMap (inputs.stream ().collect (Collectors.groupingBy (MajorPriceInput::getSku, Collectors.mapping (MajorPriceInput::getLargeAreaNo, Collectors.toList ()))),largeAreaNos);
            Map<String, Set<Integer>> availableAreaMap = skuAreaWarehouseNoMapVO.getSkuAreaNoMap ();
            if(CollectionUtils.isEmpty (availableAreaMap)){
                return null;
            }
            Set<String> skus = availableAreaMap.keySet ();
            Set<Integer> areanos = availableAreaMap.values().stream() .flatMap(Set::stream).collect(Collectors.toSet ());
            List<ProductSkuBaseResp> skuList = productFacade.querySkuInfoBySkuList (new ArrayList<> (skus));
            if(CollectionUtils.isEmpty (skuList)){
                log.info ("新增报价单返回，没有可用sku,inputs={}", JSON.toJSONString (inputs));
                return null;
            }
            Map<String, ProductSkuBaseResp> skuInfoMap = skuList.stream().collect(Collectors.toMap (ProductSkuBaseResp::getSku, Function.identity()));


            List<MajorPriceEntity> majorPriceEntities = majorPriceQueryRepository.queryListMajorPriceWithoutTime (direct,Long.valueOf (adminId), skus, areanos);
            Map<String, List<MajorPriceEntity>> dbMajorPriceMap;
            if(CollectionUtil.isNotEmpty (majorPriceEntities)){
                dbMajorPriceMap  = majorPriceEntities.stream().collect(Collectors.groupingBy(MajorPriceEntity::getSku));
            } else {
                dbMajorPriceMap = Collections.emptyMap ();
            }
            for(MajorPriceInput item : inputs){
                //获取基本信息
                String sku = item.getSku ();
                Integer largeAreaNo = item.getLargeAreaNo ();

                Set<Integer> areaNos = availableAreaMap.get (sku);
                if(CollectionUtil.isEmpty (areaNos)){
                    continue;
                }
                for(Integer areaNo : areaNos){
                    if (dbMajorPriceMap.containsKey (sku)) {
                        List<MajorPriceEntity> dbPriceList = dbMajorPriceMap.get (sku).stream ().filter (majorPriceEntity -> areaNo.equals (majorPriceEntity.getAreaNo ())).collect (Collectors.toList ());
                        if (CollectionUtil.isNotEmpty (dbPriceList)) {
                            List<MajorPriceEntity> isOverlapList = dbPriceList.stream ().filter (e -> isOverlap (e.getValidTime (), e.getInvalidTime (), validTime, invalidTime)).collect (Collectors.toList ());
                            //如果有时间重叠的，则返回提示
                            if (CollectionUtil.isNotEmpty (isOverlapList)) {
                                return String.format ("%s在%s存在报价单，如果保存数据，会覆盖已有的报价单", skuInfoMap.get (sku).getTitle (), largeAreaMap.get (largeAreaNo));
                            } else {
                                //如果有未来生效 则返回提示
                                List<MajorPriceEntity> dbs = dbPriceList.stream ().filter (e -> (e.getValidTime ().isAfter (LocalDateTime.now ())  && validTime.isAfter (LocalDateTime.now ())) || e.getStatus ().equals (MajorPriceStatusEnum.SAVE.getType ())).collect (Collectors.toList ());
                                if (CollectionUtil.isNotEmpty (dbs)) {
                                    return String.format ("%s在%s存在报价单，如果保存数据，会覆盖已有的报价单", skuInfoMap.get (sku).getTitle (), largeAreaMap.get (largeAreaNo));
                                }
                            }
                        }
                    }
                }
            }
        } else if (input.getEditType ().equals (EditTypeEnum.UPDATE.getCode ())) {
            List<Integer> ids = input.getIds ();
            List<MajorPriceEntity> majorPriceEntities = majorPriceQueryRepository.queryListMajorPriceByIds (ids.stream().map(Long::valueOf).collect(Collectors.toList()));
            if(CollectionUtil.isEmpty (majorPriceEntities)){
                throw new BizException ("报价单不存在");
            }
            Set<Integer> largeAreaNos = majorPriceEntities.stream ().map (MajorPriceEntity::getLargeAreaNo).collect (Collectors.toSet ());
            Set<String> skus = majorPriceEntities.stream ().map (MajorPriceEntity::getSku).collect (Collectors.toSet ());
            Set<Integer> areanos = majorPriceEntities.stream ().map (MajorPriceEntity::getAreaNo).collect (Collectors.toSet ());

            List<MajorPriceEntity> majorPriceEntitiesOther = majorPriceQueryRepository.queryListMajorPriceWithoutTime (direct,Long.valueOf (adminId), skus, areanos).stream().filter (e-> !ids.contains (e.getId ())).collect(Collectors.toList());

            List<LargeArea> largeAreas = areaQueryRepository.queryLargeAreaByLargeAreaNos (new ArrayList<> (largeAreaNos));
            Map<Integer, String> largeAreaMap = largeAreas.stream().collect(Collectors.toMap(LargeArea::getLargeAreaNo,LargeArea::getLargeAreaName));

            for(MajorPriceEntity e :majorPriceEntities){
                if(!CollectionUtil.isEmpty (majorPriceEntitiesOther)) {
                    List<MajorPriceEntity> sameList = majorPriceEntitiesOther.stream ().filter (i -> i.getSku ().equals (e.getSku ()) && i.getAreaNo ().equals (e.getAreaNo ())).collect (Collectors.toList ());
                    if(!CollectionUtil.isEmpty (sameList)) {
                        //比较其他的报价单 时间是否与修改的这个本报价单有交集， 如果有则删除另一个，保存此报价单
                        List<MajorPriceEntity> isOverlap = sameList.stream ().filter (i -> isOverlap (i.getValidTime (), i.getInvalidTime (), validTime, invalidTime)).collect (Collectors.toList ());
                        if(!CollectionUtil.isEmpty (isOverlap)) {
                            return String.format ("%s在%s大区存在报价单，如果保存数据，会覆盖已有的报价单", e.getPdName (), largeAreaMap.get (e.getLargeAreaNo ()));
                        }
                        //查询是否有未生效的报价单，如果修改后的时间是也是未生效，把另一个未生效的报价单删除
                        List<MajorPriceEntity> dbs = sameList.stream ().filter (i -> i.getValidTime ().isAfter (LocalDateTime.now ())).collect (Collectors.toList ());
                        if(!CollectionUtil.isEmpty (dbs)) {
                            return String.format ("%s在%s大区存在报价单，如果保存数据，会覆盖已有的报价单", e.getPdName (), largeAreaMap.get (e.getLargeAreaNo ()));
                        }
                    }
                }
            }
        }
        return null;
    }


}
