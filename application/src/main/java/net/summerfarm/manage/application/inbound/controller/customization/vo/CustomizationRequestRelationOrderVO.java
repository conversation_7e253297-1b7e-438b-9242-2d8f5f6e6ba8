package net.summerfarm.manage.application.inbound.controller.customization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-01-23 16:02:29
 * @version 1.0
 *
 */
@Data
public class CustomizationRequestRelationOrderVO {
	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 商品名称
	 */
	public String pdName;


	/**
	 * 规格
	 */
	public String weight;


	/**
	 * sku
	 */
	public String sku;

	/**
	 * 数量
	 */
	public Integer amount;

	/**
	 * 是否到货 0 未到货 1 已到货 2部分发货
	 */
	private Integer arrivalStatus;

}