package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 商品相关查询类
 */
@Data
public class SkuByLargeAreaQueryInput extends BasePageInput implements Serializable {
    /**
     * spu name
     */
    private String spuTitleLike;
    /**
     * adminId
     */
    @NotNull(message = "adminId不能为空")
    private Long adminId;
    /**
     * sku
     */
    private String itemCode;
    /**
     * 运营大区
     */
    @NotEmpty(message = "运营大区不能为空")
    private List<Integer> largeAreaNos;
    /**
     * 级别
     */
    private List<String> levelPropertyNames;
    /**
     * 品种
     */
    private List<String> varietyList;
    /**
     * sku类型
     */
    private List<Integer> extTypes;

}
