package net.summerfarm.manage.application.inbound.controller.job.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MerchantJobDetailVo {

    /**
     * primary key
     */
    private Long id;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务备注（用于后台展示）
     */
    private String remark;

    /**
     * 任务类型
     * @see net.xianmu.jobsdk.enums.CrmJobEnum.Type#getCode()
     */
    private Integer type;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务命中的品列表
     */
    @Deprecated
    private List<JobProductVO> productList;

    /**
     * 状态
     * @see net.xianmu.jobsdk.enums.CrmJobEnum.Status#getCode()
     */
    private Integer status;

    /**
     * 关联人群方式
     * @see net.xianmu.jobsdk.enums.CrmJobEnum.MerchantSelectionType#getCode()
     */
    private Integer merchantSelectionType;

    /**
     * 任务命中的品类列表。是个二维数组
     */
    private List<List<Long>> categoryList;

    /**
     * 任务领取方式：0-手动领取，1-自动领取
     */
    private Integer claimingMethod;

    /**
     * @see net.xianmu.jobsdk.enums.CrmJobEnum.CompletionCriteriaType#getCode()
     */
    private Integer completionType;

    private String completionValue;

    /**
     * 任务支持的订单类型列表：0-普通订单，1-省心送订单
     */
    private List<Integer> orderTypeList;

    /**
     * 上传的excel地址(ossKey)
     */
    private String excelUrl;

    /**
     * excel下载地址
     */
    private String excelDownloadUrl;

    /**
     * 关联人群包id列表
     */
    private List<JobMerchantPoolVO> merchantPoolList;

    /**
     * 奖励类型
     * @see net.xianmu.jobsdk.enums.CrmJobEnum.RewardType#getCode()
     */
    private Integer rewardType;

    /**
     * 任务具体的奖励：卡券id、积分等
     */
    private String rewardValue;

    /**
     * 卡券名称
     */
    private String couponName;
}
