package net.summerfarm.manage.application.inbound.provider.admin;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.admin.AdminDataPermissionQueryProvider;
import net.summerfarm.client.req.admin.AdminDataPermissionQueryReq;
import net.summerfarm.client.resp.admin.AdminDataPermissionResp;
import net.summerfarm.manage.application.inbound.provider.admin.converter.AdminDataPermissionConverter;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;
import net.summerfarm.manage.domain.admin.repository.AdminDataPermissionQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 数据权限查询服务<br/>
 * date: 2024/7/9 15:33<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
@Component
public class AdminDataPermissionQueryProviderImpl implements AdminDataPermissionQueryProvider {

    @Resource
    private AdminDataPermissionQueryRepository adminDataPermissionQueryRepository;

    @Override
    public DubboResponse<List<AdminDataPermissionResp>> queryAdminPermission(@Valid AdminDataPermissionQueryReq req) {
        AdminDataPermissionQueryParam param = new AdminDataPermissionQueryParam();
        param.setAdminId(req.getAdminId());
        param.setType(req.getType());

        return DubboResponse.getOK(adminDataPermissionQueryRepository.selectByCondition(param).stream()
                .map(AdminDataPermissionConverter::toAdminDataPermissionResp).collect(Collectors.toList()));
    }


}
