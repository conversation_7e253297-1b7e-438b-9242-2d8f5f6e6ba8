package net.summerfarm.manage.application.service.admin.impl;


import java.util.Objects;
import net.summerfarm.manage.application.service.admin.AdminDataPermissionQueryService;
import net.summerfarm.manage.domain.admin.repository.AdminDataPermissionQueryRepository;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;
import net.summerfarm.manage.application.inbound.controller.admin.input.query.AdminDataPermissionQueryInput;
import net.summerfarm.manage.application.inbound.controller.admin.assembler.AdminDataPermissionAssembler;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
*
* <AUTHOR>
* @date 2024-06-19 16:33:43
* @version 1.0
*
*/
@Service
public class AdminDataPermissionQueryServiceImpl implements AdminDataPermissionQueryService {

    @Autowired
    private AdminDataPermissionQueryRepository adminDataPermissionQueryRepository;

    @Override
    public PageInfo<AdminDataPermissionEntity> getPage(AdminDataPermissionQueryInput input) {
        AdminDataPermissionQueryParam queryParam = AdminDataPermissionAssembler.toAdminDataPermissionQueryParam(input);
        return adminDataPermissionQueryRepository.getPage(queryParam);
    }

    @Override
    public AdminDataPermissionEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return adminDataPermissionQueryRepository.selectById(id);
    }
}