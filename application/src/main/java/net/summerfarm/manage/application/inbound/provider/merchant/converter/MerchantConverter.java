package net.summerfarm.manage.application.inbound.provider.merchant.converter;

import net.summerfarm.client.resp.merchant.MerchantQueryResp;
import net.summerfarm.client.resp.merchant.MerchantShowPriceResultResp;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-01-08 10:14:32
 * @version 1.0
 *
 */
public class MerchantConverter {


    private MerchantConverter() {
        // 无需实现
    }

    public static List<MerchantShowPriceResultResp> toMerchantShowPriceResultRespList(List<MerchantEntity> merchantEntityList) {
        if (merchantEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantShowPriceResultResp> merchantShowPriceResultRespList = new ArrayList<>();
        for (MerchantEntity merchantEntity : merchantEntityList) {
            merchantShowPriceResultRespList.add(toMerchantShowPriceResultResp(merchantEntity));
        }
        return merchantShowPriceResultRespList;
    }

    public static MerchantShowPriceResultResp toMerchantShowPriceResultResp(MerchantEntity merchantEntity) {
        if (merchantEntity == null) {
            return null;
        }
        MerchantShowPriceResultResp merchantShowPriceResultResp = new MerchantShowPriceResultResp();
        merchantShowPriceResultResp.setMId(merchantEntity.getMId());
        merchantShowPriceResultResp.setShowPrice(merchantEntity.getShowPrice());

        return merchantShowPriceResultResp;
    }

    public static MerchantQueryResp toMerchantQueryResp(MerchantEntity merchantEntity) {
        if (merchantEntity == null){
            return null;
        }
        MerchantQueryResp merchantQueryResp = new MerchantQueryResp();
        merchantQueryResp.setMId(merchantEntity.getMId());
        merchantQueryResp.setMname(merchantEntity.getMname());
        merchantQueryResp.setMcontact(merchantEntity.getMcontact());
        merchantQueryResp.setPhone(merchantEntity.getPhone());
        merchantQueryResp.setPoiNote(merchantEntity.getPoiNote());
        merchantQueryResp.setAreaNo(merchantEntity.getAreaNo());
        merchantQueryResp.setDirect(merchantEntity.getDirect());
        merchantQueryResp.setSkuShow(merchantEntity.getSkuShow());
        merchantQueryResp.setSize(merchantEntity.getSize());
        merchantQueryResp.setEnterpriseScale(merchantEntity.getEnterpriseScale());
        merchantQueryResp.setCompanyBrand(merchantEntity.getCompanyBrand());
        merchantQueryResp.setType(merchantEntity.getType());
        merchantQueryResp.setGrade(merchantEntity.getGrade());
        merchantQueryResp.setMerchantType(merchantEntity.getMerchantType());
        merchantQueryResp.setOpenId(merchantEntity.getOpenid());
        merchantQueryResp.setAdminId(merchantEntity.getAdminId());
        return merchantQueryResp;
    }
}
