package net.summerfarm.manage.application.inbound.controller.product.assembler;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.application.inbound.controller.product.vo.AppPopTopMatchedCompetitorSkuListVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.TopMatchedCompetitorSkuVO;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopTopMatchedCompetitorSkuListCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopTopMatchedCompetitorSkuListQueryInput;
import net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam;
import net.summerfarm.manage.domain.product.param.command.AppPopTopMatchedCompetitorSkuListCommandParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
@Slf4j
public class AppPopTopMatchedCompetitorSkuListAssembler {

    private AppPopTopMatchedCompetitorSkuListAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static AppPopTopMatchedCompetitorSkuListQueryParam toAppPopTopMatchedCompetitorSkuListQueryParam(AppPopTopMatchedCompetitorSkuListQueryInput appPopTopMatchedCompetitorSkuListQueryInput) {
        if (appPopTopMatchedCompetitorSkuListQueryInput == null) {
            return null;
        }
        AppPopTopMatchedCompetitorSkuListQueryParam appPopTopMatchedCompetitorSkuListQueryParam = new AppPopTopMatchedCompetitorSkuListQueryParam();
        appPopTopMatchedCompetitorSkuListQueryParam.setId(appPopTopMatchedCompetitorSkuListQueryInput.getId());
        appPopTopMatchedCompetitorSkuListQueryParam.setSku(appPopTopMatchedCompetitorSkuListQueryInput.getSku());
        appPopTopMatchedCompetitorSkuListQueryParam.setWeight(appPopTopMatchedCompetitorSkuListQueryInput.getWeight());
        appPopTopMatchedCompetitorSkuListQueryParam.setSkuName(appPopTopMatchedCompetitorSkuListQueryInput.getSkuName());
        appPopTopMatchedCompetitorSkuListQueryParam.setPdName(appPopTopMatchedCompetitorSkuListQueryInput.getPdName());
        appPopTopMatchedCompetitorSkuListQueryParam.setTopMatchedCompetitorSkuList(appPopTopMatchedCompetitorSkuListQueryInput.getTopMatchedCompetitorSkuList());
        appPopTopMatchedCompetitorSkuListQueryParam.setCategory(appPopTopMatchedCompetitorSkuListQueryInput.getCategory());
        appPopTopMatchedCompetitorSkuListQueryParam.setGmv(appPopTopMatchedCompetitorSkuListQueryInput.getGmv());
        appPopTopMatchedCompetitorSkuListQueryParam.setOrderCnt(appPopTopMatchedCompetitorSkuListQueryInput.getOrderCnt());
        appPopTopMatchedCompetitorSkuListQueryParam.setOrderQuantity(appPopTopMatchedCompetitorSkuListQueryInput.getOrderQuantity());
        appPopTopMatchedCompetitorSkuListQueryParam.setLastOrderTime(appPopTopMatchedCompetitorSkuListQueryInput.getLastOrderTime());
        appPopTopMatchedCompetitorSkuListQueryParam.setCreateTime(appPopTopMatchedCompetitorSkuListQueryInput.getCreateTime());
        appPopTopMatchedCompetitorSkuListQueryParam.setDs(appPopTopMatchedCompetitorSkuListQueryInput.getDs());
        appPopTopMatchedCompetitorSkuListQueryParam.setPageIndex(appPopTopMatchedCompetitorSkuListQueryInput.getPageIndex());
        appPopTopMatchedCompetitorSkuListQueryParam.setPageSize(appPopTopMatchedCompetitorSkuListQueryInput.getPageSize());
        return appPopTopMatchedCompetitorSkuListQueryParam;
    }





    public static AppPopTopMatchedCompetitorSkuListCommandParam buildCreateParam(AppPopTopMatchedCompetitorSkuListCommandInput appPopTopMatchedCompetitorSkuListCommandInput) {
        if (appPopTopMatchedCompetitorSkuListCommandInput == null) {
            return null;
        }
        AppPopTopMatchedCompetitorSkuListCommandParam appPopTopMatchedCompetitorSkuListCommandParam = new AppPopTopMatchedCompetitorSkuListCommandParam();
        appPopTopMatchedCompetitorSkuListCommandParam.setId(appPopTopMatchedCompetitorSkuListCommandInput.getId());
        appPopTopMatchedCompetitorSkuListCommandParam.setSku(appPopTopMatchedCompetitorSkuListCommandInput.getSku());
        appPopTopMatchedCompetitorSkuListCommandParam.setWeight(appPopTopMatchedCompetitorSkuListCommandInput.getWeight());
        appPopTopMatchedCompetitorSkuListCommandParam.setSkuName(appPopTopMatchedCompetitorSkuListCommandInput.getSkuName());
        appPopTopMatchedCompetitorSkuListCommandParam.setPdName(appPopTopMatchedCompetitorSkuListCommandInput.getPdName());
        appPopTopMatchedCompetitorSkuListCommandParam.setTopMatchedCompetitorSkuList(appPopTopMatchedCompetitorSkuListCommandInput.getTopMatchedCompetitorSkuList());
        appPopTopMatchedCompetitorSkuListCommandParam.setCategory(appPopTopMatchedCompetitorSkuListCommandInput.getCategory());
        appPopTopMatchedCompetitorSkuListCommandParam.setGmv(appPopTopMatchedCompetitorSkuListCommandInput.getGmv());
        appPopTopMatchedCompetitorSkuListCommandParam.setOrderCnt(appPopTopMatchedCompetitorSkuListCommandInput.getOrderCnt());
        appPopTopMatchedCompetitorSkuListCommandParam.setOrderQuantity(appPopTopMatchedCompetitorSkuListCommandInput.getOrderQuantity());
        appPopTopMatchedCompetitorSkuListCommandParam.setLastOrderTime(appPopTopMatchedCompetitorSkuListCommandInput.getLastOrderTime());
        appPopTopMatchedCompetitorSkuListCommandParam.setCreateTime(appPopTopMatchedCompetitorSkuListCommandInput.getCreateTime());
        appPopTopMatchedCompetitorSkuListCommandParam.setDs(appPopTopMatchedCompetitorSkuListCommandInput.getDs());
        return appPopTopMatchedCompetitorSkuListCommandParam;
    }


    public static AppPopTopMatchedCompetitorSkuListCommandParam buildUpdateParam(AppPopTopMatchedCompetitorSkuListCommandInput appPopTopMatchedCompetitorSkuListCommandInput) {
        if (appPopTopMatchedCompetitorSkuListCommandInput == null) {
            return null;
        }
        AppPopTopMatchedCompetitorSkuListCommandParam appPopTopMatchedCompetitorSkuListCommandParam = new AppPopTopMatchedCompetitorSkuListCommandParam();
        appPopTopMatchedCompetitorSkuListCommandParam.setId(appPopTopMatchedCompetitorSkuListCommandInput.getId());
        appPopTopMatchedCompetitorSkuListCommandParam.setSku(appPopTopMatchedCompetitorSkuListCommandInput.getSku());
        appPopTopMatchedCompetitorSkuListCommandParam.setWeight(appPopTopMatchedCompetitorSkuListCommandInput.getWeight());
        appPopTopMatchedCompetitorSkuListCommandParam.setSkuName(appPopTopMatchedCompetitorSkuListCommandInput.getSkuName());
        appPopTopMatchedCompetitorSkuListCommandParam.setPdName(appPopTopMatchedCompetitorSkuListCommandInput.getPdName());
        appPopTopMatchedCompetitorSkuListCommandParam.setTopMatchedCompetitorSkuList(appPopTopMatchedCompetitorSkuListCommandInput.getTopMatchedCompetitorSkuList());
        appPopTopMatchedCompetitorSkuListCommandParam.setCategory(appPopTopMatchedCompetitorSkuListCommandInput.getCategory());
        appPopTopMatchedCompetitorSkuListCommandParam.setGmv(appPopTopMatchedCompetitorSkuListCommandInput.getGmv());
        appPopTopMatchedCompetitorSkuListCommandParam.setOrderCnt(appPopTopMatchedCompetitorSkuListCommandInput.getOrderCnt());
        appPopTopMatchedCompetitorSkuListCommandParam.setOrderQuantity(appPopTopMatchedCompetitorSkuListCommandInput.getOrderQuantity());
        appPopTopMatchedCompetitorSkuListCommandParam.setLastOrderTime(appPopTopMatchedCompetitorSkuListCommandInput.getLastOrderTime());
        appPopTopMatchedCompetitorSkuListCommandParam.setCreateTime(appPopTopMatchedCompetitorSkuListCommandInput.getCreateTime());
        appPopTopMatchedCompetitorSkuListCommandParam.setDs(appPopTopMatchedCompetitorSkuListCommandInput.getDs());
        return appPopTopMatchedCompetitorSkuListCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<AppPopTopMatchedCompetitorSkuListVO> toAppPopTopMatchedCompetitorSkuListVOList(List<AppPopTopMatchedCompetitorSkuListEntity> appPopTopMatchedCompetitorSkuListEntityList) {
        if (appPopTopMatchedCompetitorSkuListEntityList == null) {
            return Collections.emptyList();
        }
        List<AppPopTopMatchedCompetitorSkuListVO> appPopTopMatchedCompetitorSkuListVOList = new ArrayList<>();
        for (AppPopTopMatchedCompetitorSkuListEntity appPopTopMatchedCompetitorSkuListEntity : appPopTopMatchedCompetitorSkuListEntityList) {
            appPopTopMatchedCompetitorSkuListVOList.add(toAppPopTopMatchedCompetitorSkuListVO(appPopTopMatchedCompetitorSkuListEntity));
        }
        return appPopTopMatchedCompetitorSkuListVOList;
}


   public static AppPopTopMatchedCompetitorSkuListVO toAppPopTopMatchedCompetitorSkuListVO(AppPopTopMatchedCompetitorSkuListEntity appPopTopMatchedCompetitorSkuListEntity) {
       if (appPopTopMatchedCompetitorSkuListEntity == null) {
            return null;
       }
       AppPopTopMatchedCompetitorSkuListVO appPopTopMatchedCompetitorSkuListVO = new AppPopTopMatchedCompetitorSkuListVO();
       appPopTopMatchedCompetitorSkuListVO.setId(appPopTopMatchedCompetitorSkuListEntity.getId());
       appPopTopMatchedCompetitorSkuListVO.setSku(appPopTopMatchedCompetitorSkuListEntity.getSku());
       appPopTopMatchedCompetitorSkuListVO.setWeight(appPopTopMatchedCompetitorSkuListEntity.getWeight());
       appPopTopMatchedCompetitorSkuListVO.setSkuName(appPopTopMatchedCompetitorSkuListEntity.getSkuName());
       appPopTopMatchedCompetitorSkuListVO.setPdName(appPopTopMatchedCompetitorSkuListEntity.getPdName());
       appPopTopMatchedCompetitorSkuListVO.setTopMatchedCompetitorSkuList(appPopTopMatchedCompetitorSkuListEntity.getTopMatchedCompetitorSkuList());
       appPopTopMatchedCompetitorSkuListVO.setCategory(appPopTopMatchedCompetitorSkuListEntity.getCategory());
       appPopTopMatchedCompetitorSkuListVO.setGmv(appPopTopMatchedCompetitorSkuListEntity.getGmv());
       appPopTopMatchedCompetitorSkuListVO.setOrderCnt(appPopTopMatchedCompetitorSkuListEntity.getOrderCnt());
       appPopTopMatchedCompetitorSkuListVO.setOrderQuantity(appPopTopMatchedCompetitorSkuListEntity.getOrderQuantity());
       appPopTopMatchedCompetitorSkuListVO.setLastOrderTime(appPopTopMatchedCompetitorSkuListEntity.getLastOrderTime());
       appPopTopMatchedCompetitorSkuListVO.setCreateTime(appPopTopMatchedCompetitorSkuListEntity.getCreateTime());
       appPopTopMatchedCompetitorSkuListVO.setDs(appPopTopMatchedCompetitorSkuListEntity.getDs());
       appPopTopMatchedCompetitorSkuListVO.setTopMatchedCompetitorSkuVOList(convert(appPopTopMatchedCompetitorSkuListEntity));
       appPopTopMatchedCompetitorSkuListVO.setExternalSkuCode(appPopTopMatchedCompetitorSkuListEntity.getExternalSkuCode());
       return appPopTopMatchedCompetitorSkuListVO;
   }

   public static List<TopMatchedCompetitorSkuVO> convert(AppPopTopMatchedCompetitorSkuListEntity appPopTopMatchedCompetitorSkuListEntity) {
        if (Objects.isNull(appPopTopMatchedCompetitorSkuListEntity)
                || CollectionUtils.isEmpty(appPopTopMatchedCompetitorSkuListEntity.getCompetitorSkuEntityList())) {
            return Lists.newArrayList();
        }
        List<TopMatchedCompetitorSkuVO> result = Lists.newArrayList();
       appPopTopMatchedCompetitorSkuListEntity.getCompetitorSkuEntityList().forEach(competitorSkuEntity -> {
           if (Objects.isNull(competitorSkuEntity)) {
               return;
           }
           TopMatchedCompetitorSkuVO competitorSkuVO = TopMatchedCompetitorSkuVO.builder()
                   .rank(competitorSkuEntity.getRank())
                   .competitor(competitorSkuEntity.getCompetitor())
                   .competitorSkuCode(competitorSkuEntity.getCompetitorSkuCode())
                   .competitorSkuPrice(competitorSkuEntity.getCompetitorSkuPrice())
                   .similarityScore(competitorSkuEntity.getSimilarityScore())
                   .matchingReason(competitorSkuEntity.getMatchingReason())
                   .competitorProductName(competitorSkuEntity.getCompetitorProductName())
                   .grossWeight(competitorSkuEntity.getGrossWeight())
                   .goodsName(competitorSkuEntity.getGoodsName())
                   .netWeight(competitorSkuEntity.getNetWeight())
                   .specification(competitorSkuEntity.getSpecification()).build();
           result.add(competitorSkuVO);

       });
       return result;
   }

}
