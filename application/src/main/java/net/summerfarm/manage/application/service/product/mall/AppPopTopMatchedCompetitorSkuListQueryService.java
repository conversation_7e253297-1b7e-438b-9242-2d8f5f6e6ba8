package net.summerfarm.manage.application.service.product.mall;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopTopMatchedCompetitorSkuListQueryInput;

/**
 *
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
public interface AppPopTopMatchedCompetitorSkuListQueryService {

    /**
     * @description: 分页
     * @return AppPopTopMatchedCompetitorSkuListEntity
     **/
    PageInfo<AppPopTopMatchedCompetitorSkuListEntity> getPage(AppPopTopMatchedCompetitorSkuListQueryInput input);

    /**
     * @description: 详情
     * @return: java.lang.Boolean
     **/
    AppPopTopMatchedCompetitorSkuListEntity getDetail(Long id);

    /**
     * 获取一条鲜果POP数据
     *
     * <AUTHOR>
     * @date 2024/12/2 14:05
     */
    AppPopTopMatchedCompetitorSkuListEntity getOne(AppPopTopMatchedCompetitorSkuListQueryInput input);
}