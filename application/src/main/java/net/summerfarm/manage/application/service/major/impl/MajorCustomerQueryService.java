package net.summerfarm.manage.application.service.major.impl;


import com.github.pagehelper.PageInfo;
import jdk.nashorn.internal.ir.annotations.Reference;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.major.assembler.MajorPriceLogAssembler;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceLogQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorCustomerDetailVO;
import net.summerfarm.manage.application.service.major.converter.MajorCustomerConvert;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.area.valueobject.LargeAreaValueObject;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import net.summerfarm.manage.domain.major.param.query.MajorPriceLogQueryParam;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.repository.MajorPriceQueryRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * @date 2025-02-19 11:19:11
 * @version 1.0
 *
 */
@Service
@Slf4j
public class MajorCustomerQueryService {

    @Resource
    private AdminQueryRepository adminQueryRepository;
    @Resource
    private MajorPriceQueryRepository majorPriceQueryRepository;

    public MajorCustomerDetailVO getDetail(Long adminId) {
        AdminEntity adminEntity = adminQueryRepository.selectByPrimaryKey(adminId);
        if(adminEntity == null) {
            log.info("大客户不存在！adminId:{}", adminId);
            throw new BizException("大客户不存在!");
        }
        MajorCustomerDetailVO detailVO = MajorCustomerConvert.INSTANCE.buildMajorCustomerDetailVO(adminEntity);
        // 大客户合作大区
        List<LargeAreaValueObject> largeAreaValueObjects = adminQueryRepository.selectMajorCustomerCooperationLargeAreaNo(adminId);
        detailVO.setLargeAreaList(largeAreaValueObjects);

        // 最近一次报价的生效时间、失效时间
        MajorPriceEntity majorPriceEntity = majorPriceQueryRepository.selectLastCommitMajorPrice(adminId);
        if(majorPriceEntity != null) {
            detailVO.setLastCommitValidTime(majorPriceEntity.getValidTime());
            detailVO.setLastCommitInvalidTime(majorPriceEntity.getInvalidTime());
        }
        return detailVO;
    }
}