package net.summerfarm.manage.application.service.product.mall;

import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.ExternalProductMappingCommandInput;


/**
 * @date 2024-11-15 14:13:27
 * @version 1.0
 */
public interface ExternalProductMappingCommandService {

    /**
     * @description: 新增
     * @return ExternalProductMappingEntity
     **/
    ExternalProductMappingEntity insert(ExternalProductMappingCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    void update(ExternalProductMappingCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

}