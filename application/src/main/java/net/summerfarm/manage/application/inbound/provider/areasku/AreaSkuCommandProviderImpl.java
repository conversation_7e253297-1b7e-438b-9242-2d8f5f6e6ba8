package net.summerfarm.manage.application.inbound.provider.areasku;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.areasku.AreaSkuCommandProvider;
import net.summerfarm.client.req.areasku.AreaSkuCloseSaleReq;
import net.summerfarm.client.req.areasku.AreaSkuOpenSaleReq;
import net.summerfarm.client.req.areasku.AreaSkuPriceCommandReq;
import net.summerfarm.manage.application.inbound.provider.areasku.converter.AreaSkuConverter;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Slf4j
@DubboService
@Component
public class AreaSkuCommandProviderImpl implements AreaSkuCommandProvider {
    @Autowired
    private AreaSkuQueryRepository areaSkuQueryRepository;
    @Autowired
    private AreaSkuCommandService areaSkuCommandService;
    @Autowired
    private NacosPropertiesHolder nacosPropertiesHolder;
    @Override
    public DubboResponse updateAreaSkuPrice(AreaSkuPriceCommandReq req) {
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.queryListSkuPrice (Collections.singletonList (req.getSku ()), req.getAreaNoList (), null);
        if(CollectionUtil.isEmpty (areaSkuEntities)){
            log.warn ("pop_price_effect失败,sku={},areaNoList={}",req.getSku (),req.getAreaNoList ());
            throw new BizException ("更新失败");
        }
        areaSkuCommandService.updateOrAddAreaSkuPrice (AreaSkuConverter.INSTANCE.areaSkuPriceCommandReq2AreaSkuPriceDTO (req));
        return DubboResponse.getOK ();
    }

    @Override
    public DubboResponse<Void> closeSaleAreaSku(AreaSkuCloseSaleReq req) {
        // 下架
        areaSkuCommandService.updateAreaSkuOnSale(req.getSku(), req.getWarehouseNo(), false);
        return DubboResponse.getOK ();
    }

    @Override
    public DubboResponse<Void> openSaleAreaSku(@Valid AreaSkuOpenSaleReq req) {
        // 上架
        areaSkuCommandService.updateAreaSkuOnSale(req.getSku(), req.getWarehouseNo(), true);
        return DubboResponse.getOK ();
    }

    @Override
    public DubboResponse<Void> closeSaleAreaSkuBatch(String sku, Set<Integer> areaNos){
        if(StringUtils.isBlank (sku) || CollectionUtil.isEmpty (areaNos)){
            return DubboResponse.getOK ();
        }
        // 下架
        areaSkuCommandService.updateAreaSkuOnSaleBatch(sku,areaNos, false);
        return DubboResponse.getOK ();
    }

    @Override
    public DubboResponse<Void> openSaleAreaSkuBatch(String sku, Set<Integer> areaNos){
        if(StringUtils.isBlank (sku) || CollectionUtil.isEmpty (areaNos)){
            return DubboResponse.getOK ();
        }
        // 上架
        areaSkuCommandService.updateAreaSkuOnSaleBatch(sku,areaNos, true);
        return DubboResponse.getOK ();
    }
}
