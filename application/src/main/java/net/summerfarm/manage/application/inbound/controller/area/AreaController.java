package net.summerfarm.manage.application.inbound.controller.area;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import net.summerfarm.manage.application.inbound.controller.area.input.LargeAreaQueryInput;
import net.summerfarm.manage.application.inbound.controller.area.vo.LargeAreaWithSubAreaVO;
import net.summerfarm.manage.application.service.area.AreaQueryService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @Title 运营服务区相关的接口
 * @Description 运营服务区列表、更新接口
 * @date 2024-06-18 13:21:08
 */
@RestController
@RequestMapping("/area")
public class AreaController {

    @Resource
    private AreaQueryService areaQueryService;


    /**
     * 获取系统所有的运营大区以及它们的附属运营服务区列表(支持分页)
     *
     * @param pageIndex
     * @param pageSize
     * @param status
     * @return
     */
    @GetMapping({"/larges/{pageIndex}/{pageSize}"})
    public CommonResult<PageInfo<LargeAreaWithSubAreaVO>> listPagingLargeAreas(@PathVariable Integer pageIndex, @PathVariable Integer pageSize,
        @RequestParam(required = false) Integer status) {
        LargeAreaQueryInput input = new LargeAreaQueryInput();
        input.setPageIndex(pageIndex == null ? 1 : pageIndex);
        input.setPageSize(pageSize == null ? 1000 : pageSize);
        input.setStatus(status);
        return CommonResult.ok(areaQueryService.queryAllLargeAreas(input));
    }

    /**
     * 获取系统所有的运营大区以及它们的附属运营服务区列表(无分页-全量)
     *
     * @param status
     * @return
     */
    @GetMapping("/larges")
    public CommonResult<List<LargeAreaWithSubAreaVO>> listAllLargeAreas(@RequestParam(required = false) Integer status) {
        LargeAreaQueryInput input = new LargeAreaQueryInput();
        input.setPageIndex(1);
        input.setPageSize(2000);// 默认最多2000个
        input.setStatus(status);
        PageInfo<LargeAreaWithSubAreaVO> listAllLargeAreas = areaQueryService.queryAllLargeAreas(input);
        if (null != listAllLargeAreas && CollectionUtils.isNotEmpty(listAllLargeAreas.getList())) {
            return CommonResult.ok(listAllLargeAreas.getList());
        }
        return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, String.format("未找到状态为:%d的大区数据", status));
    }
}
