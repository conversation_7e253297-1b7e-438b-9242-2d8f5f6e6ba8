package net.summerfarm.manage.application.inbound.controller.job.input.command;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-12-18 15:57:19
 * @version 1.0
 *
 */
@Data
public class CrmJobCompletionCriteriaCommandInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 任务id FK crm_job
	 */
	private Long jobId;

	/**
	 * 判定类型 0.卡券是否核销 1.拜访类型 2. 任意商品单笔下单实付金额>= xx元 3. 任意商品累计下单实付金额>= xx元
	 */
	private Integer completionType;

	/**
	 * 完成判定值（卡券id或拜访类型或金额）
	 */
	private String completionValue;

	/**
	 * 任务支持的订单类型列表：0：全部都支持，1-普通订单，2-省心送订单
	 */
	private String orderTypeList;



}