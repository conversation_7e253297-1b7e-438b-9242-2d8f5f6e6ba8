package net.summerfarm.manage.application.service.product.mall.impl;
import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.manage.application.inbound.controller.product.input.*;
import net.summerfarm.manage.application.service.product.mall.AreaSkuQueryService;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.service.AdminDomainService;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryGroupByLargeAreaParam;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationProductQueryParam;
import net.summerfarm.manage.facade.inventory.ProductCostQueryFacade;
import net.summerfarm.manage.facade.inventory.SaleInventoryCenterQueryFacade;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.ProductsPropertyEnum;
import net.summerfarm.manage.application.inbound.controller.product.vo.*;
import net.summerfarm.manage.application.service.product.converter.*;
import net.summerfarm.manage.application.service.product.mall.InventoryQueryService;
import net.summerfarm.manage.common.enums.InventoryExtTypeEnum;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.repository.*;
import net.summerfarm.manage.facade.market.MarketItemLabelFacade;
import net.summerfarm.manage.facade.market.dto.MarketItemDetailDTO;
import net.summerfarm.manage.facade.market.input.MarketItemDetailQueryInput;
import net.summerfarm.manage.facade.wms.CabinetInventoryFacade;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.authentication.common.utils.AuthUserUtils;
import net.xianmu.common.exception.BizException;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InventoryQueryServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 16:15 2024/4/30
 * @Version 1.0
 **/
@Slf4j
@Service
public class InventoryQueryServiceImpl implements InventoryQueryService {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ProductsPropertyValueQueryRepository productsPropertyValueQueryRepository;

    @Resource
    private InventoryQueryRepository inventoryQueryRepository;

    @Resource
    private ProductsQueryRepository productsQueryRepository;

    @Resource
    private ProductsPropertyMappingQueryRepository propertyMappingQueryRepository;

    @Resource
    private AreaSkuQueryRepository areaSkuQueryRepository;

    @Resource
    private InventoryBindQueryRepository inventoryBindQueryRepository;

    @Resource
    private ProductLabelValueQueryRepository productLabelValueQueryRepository;

    @Resource
    private MarketItemLabelFacade marketItemLabelFacade;

    @Resource
    private CategoryQueryRepository categoryQueryRepository;

    @Resource
    private ProductCostQueryFacade productCostQueryFacade;

    @Autowired
    private AreaSkuQueryService areaSkuQueryService;

    @Autowired
    private SaleInventoryCenterQueryFacade saleInventoryCenterQueryFacade;

    @Resource
    private AdminDomainService adminDomainService;

    @Resource
    private CabinetInventoryFacade cabinetInventoryFacade;

    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    @Override
    public void vaild(InventoryVaildInput input) {
        Long bizUserId = AuthUserUtils.getBizUserId();
        if (bizUserId == null) {
            throw new BizException("LOGIN_FIRST");
        }
        String dataPermissionKey = String.format("auth-data-permission:%s", bizUserId);
        if (redisTemplate.hasKey(dataPermissionKey)) {
            String dataPermissionStr = (String) redisTemplate.opsForValue().get(dataPermissionKey);
            Set<Integer> dataPermission = JSONObject.parseObject(dataPermissionStr, Set.class);

            // 0代表拥有所有数据权限
            if (dataPermission.contains(0)) {
                return;
            }

            //是否超级管理员
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
            if (user != null) {
                Set<Integer> roleIds = new HashSet<>(user.getRoleIds());
                if (roleIds.contains(1)) {
                    return;
                }
            }
        }
        
        //查询权限中不包含的仓库
    }

    @Override
    public ProductVO queryInfo(ProductInfoInput input) {
        //查询spu信息
        ProductsEntity productsEntity = productsQueryRepository.selectById(input.getPdId());
        if (productsEntity == null) {
            throw new BizException("SPU不存在");
        }
        ProductVO productVO = ProductResultConverter.productsEntityToProductVO(productsEntity);

        CategoryEntity categoryEntity = categoryQueryRepository.selectByCategoryId(productsEntity.getCategoryId().longValue());
        if (Objects.nonNull(categoryEntity)) {
            productVO.setCategoryType(categoryEntity.getType());
        }

        //上新状态判断
        if (Objects.equals(-1, productVO.getOutdated()) && Objects.equals(0, productVO.getAuditStatus())) {
            productVO.setCreateStatus(0);
        } else if (Objects.equals(1, productVO.getOutdated()) && Objects.equals(1, productVO.getAuditStatus())) {
            productVO.setCreateStatus(1);
        } else if (Objects.equals(-1, productVO.getOutdated()) && Objects.equals(2, productVO.getAuditStatus())) {
            productVO.setCreateStatus(2);
        }

        //关键属性值
        List<ProductsPropertyValueEntity> productsPropertyValueEntities = productsPropertyValueQueryRepository.listBySkuAndPdid(null, input.getPdId());
        List<ProductsPropertyValueVO> valueList = ProductsPropertyValueConverter.entityToVO(productsPropertyValueEntities);
        productVO.setKeyValueList(valueList);

        //销售属性
        List<ProductsPropertyEntity> propertyEntityList = propertyMappingQueryRepository.selectAnchoredProperty(1, input.getPdId().intValue());
        List<ProductsPropertyVO> propertyList = ProductsPropertyMappingConverter.entityToVOS(propertyEntityList);
        productVO.setSalePropertyList(propertyList);

        //查询sku信息
        InventoryQueryParam param = InventoryConverter.queryInfoInputToParam(input);
        List<InventoryEntity> inventoryEntityList = inventoryQueryRepository.queryInfo(param);
        List<InventoryDetailVO> inventoryDetailVOList = new ArrayList<>(inventoryEntityList.size());
        List<Long> outIds = new ArrayList<>(inventoryEntityList.size());
        for (InventoryEntity inventoryEntity : inventoryEntityList) {
            InventoryDetailVO vo = InventoryConverter.entityToVO(inventoryEntity);

            //区域sku信息
            List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.selectVOList(vo.getSku(), input.getAreaNo());
            List<AreaSkuVO> areaSkuVOS = AreaSkuConverter.entityToVO(areaSkuEntities);
            vo.setAreaSkuVOS(areaSkuVOS);

            //获取sku标签--老逻辑
            List<ProductLabelValueEntity> productLabelValueEntities = productLabelValueQueryRepository.selectBySku(vo.getSku());
            List<ProductLabelValueVO> productLabelValueVos = ProductLabelValueConverter.entityToVO(productLabelValueEntities);
            vo.setProductLabelValueVos(productLabelValueVos);

            //销售属性值
            List<ProductsPropertyValueEntity> propertyValueEntities = productsPropertyValueQueryRepository.listBySkuAndPdid(vo.getSku(), null);
            List<ProductsPropertyValueVO> saleValueList = ProductsPropertyValueConverter.entityToVO(propertyValueEntities);
            vo.setSaleValueList(saleValueList);
            Integer extType = vo.getExtType();
            if (extType != null && (Objects.equals(extType, InventoryExtTypeEnum.TEMPORARY_INSURANCE.type())
                    || Objects.equals(extType, InventoryExtTypeEnum.BROKEN_BAG.type()))) {
                InventoryBindQueryParam pram = new InventoryBindQueryParam();
                pram.setPdId(vo.getPdId());
                pram.setSku(vo.getSku());
                InventoryBindEntity inventoryBind = inventoryBindQueryRepository.selectOneByCondition(pram);
                if (inventoryBind != null) {
                    vo.setBindSku(inventoryBind.getBindSku());
                }
            }

            //上新状态判断
            if (Objects.equals(-1, vo.getOutdated()) && Objects.equals(0, vo.getAuditStatus())) {
                vo.setCreateStatus(0);
            } else if (Objects.equals(0, vo.getOutdated()) && Objects.equals(1, vo.getAuditStatus())) {
                vo.setCreateStatus(1);
            } else if (Objects.equals(-1, vo.getOutdated()) && Objects.equals(2, vo.getAuditStatus())) {
                vo.setCreateStatus(2);
            } else if (Objects.equals(1, vo.getOutdated()) && Objects.equals(1, vo.getAuditStatus())) {
                // outdated 状态为1 就是回收站sku数据
                vo.setCreateStatus(3);
            }
            inventoryDetailVOList.add(vo);
            outIds.add(vo.getInvId());
        }

        //查询商品标签--走商品中心查询
        if (CollectionUtils.isNotEmpty(outIds)) {
            MarketItemDetailQueryInput queryInput = new MarketItemDetailQueryInput();
            queryInput.setOutIds(outIds);
            List<MarketItemDetailDTO> itemDetailByOutId = marketItemLabelFacade.getItemDetailByOutId(queryInput);
            if (CollectionUtils.isNotEmpty(itemDetailByOutId)) {
                Map<Long, MarketItemDetailDTO> itemDetailDTOMap = itemDetailByOutId.stream().collect(Collectors.toMap(MarketItemDetailDTO::getOutId,
                        Function.identity(), (oldValue, newValue) -> newValue));

                inventoryDetailVOList.stream().forEach(inventoryDetailVO -> {
                    MarketItemDetailDTO marketItemDetailDTO = itemDetailDTOMap.get(inventoryDetailVO.getInvId());
                    if (marketItemDetailDTO != null) {
                        inventoryDetailVO.setItemLabel(marketItemDetailDTO.getItemLabel());
                    }
                });
            }
        }

        //审批状态处理
        if (Objects.equals(input.getOutdated(), -1) && !CollectionUtils.isEmpty(inventoryDetailVOList)) {
            productVO.setAuditStatus(inventoryEntityList.get(0).getAuditStatus());
        }

        productVO.setInventoryDetailVOS(inventoryDetailVOList);
        return productVO;
    }

    @Override
    public List<InventoryBaseVO> listSkuBaseInfo(InventoryBaseQueryInput input) {
        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setSku(input.getSku());
        queryParam.setSkuList(input.getSkuList());
        queryParam.setSubType(input.getSubType());
        // 商品信息
        List<InventoryEntity> inventoryEntityList = inventoryQueryRepository.queryInfo(queryParam);
        if (CollectionUtils.isEmpty(inventoryEntityList)) {
            return Lists.newArrayList();
        }
        // 类目信息
        List<Long> categoryIdList = inventoryEntityList.stream().map(InventoryEntity::getCategoryId).distinct().collect(Collectors.toList());
        List<CategoryAllPathEntity> categoryAllPathEntityList = categoryQueryRepository.selectCategoryAllPath(categoryIdList);
        Map<Long, CategoryAllPathEntity> categoryAllPathEntityMap = categoryAllPathEntityList.stream()
                .collect(Collectors.toMap(CategoryAllPathEntity::getCategoryId, Function.identity(), (a, b) -> a));
        // 属性信息
        List<String> skuList = inventoryEntityList.stream().map(InventoryEntity::getSku).distinct().collect(Collectors.toList());
        List<ProductsPropertyValueEntity> propertyValueEntities = productsPropertyValueQueryRepository.selectSaleValueBySkuList(skuList);
        Map<String, ProductsPropertyValueEntity> fruitSizeMap = propertyValueEntities.stream()
                .filter(propertyValue -> Objects.equals(ProductsPropertyEnum.FRUIT_SIZE.getType(),propertyValue.getProductsPropertyId()))
                .collect(Collectors.toMap(ProductsPropertyValueEntity::getSku, Function.identity(), (a, b) -> a));
        List<InventoryBaseVO> result = Lists.newArrayList();
        inventoryEntityList.forEach(inventoryEntity -> {
            CategoryAllPathEntity categoryAllPathEntity = categoryAllPathEntityMap.get(inventoryEntity.getCategoryId());
            ProductsPropertyValueEntity fruitSizeEntity = fruitSizeMap.get(inventoryEntity.getSku());
            InventoryBaseVO inventoryBaseVO = InventoryConverter.convert(inventoryEntity, categoryAllPathEntity, fruitSizeEntity);
            result.add(inventoryBaseVO);
        });
        return result;
    }

    @Override
    public PageInfo<MarketItemByLargeAreaListVO> pageSkuByLargeArea(SkuByLargeAreaQueryInput input) {
        if (input.getPageIndex() == null || input.getPageSize() == null) {
            input.setPageIndex(1);
            input.setPageSize(10);
        }
        InventoryQueryGroupByLargeAreaParam param = new InventoryQueryGroupByLargeAreaParam ();
        param.setItemCode(input.getItemCode ());
        param.setLargeAreaNos(input.getLargeAreaNos ());
        param.setLevelPropertyNames(input.getLevelPropertyNames ());
        param.setVarietyList(input.getVarietyList ());
        param.setExtTypes(input.getExtTypes ());
        param.setAdminId (input.getAdminId ());
        param.setPageIndex (input.getPageIndex ());
        param.setPageSize (input.getPageSize ());
        if(StringUtils.isNotBlank (input.getSpuTitleLike ())){
            List<String> skus = inventoryQueryRepository.querySkusByNameLike(input.getSpuTitleLike ());
            if(CollectionUtil.isEmpty (skus)){
                return new PageInfo<> (Collections.emptyList());
            }
            param.setSkus (skus);
        }
        PageInfo pageInfo = inventoryQueryRepository.listByParamGroupByLargeArea(param);
        if (pageInfo == null || CollectionUtil.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }

        List<InvetoryGroupByLargeAreaNoEntity> entities = pageInfo.getList ();
        List<MarketItemByLargeAreaListVO> list = MarketResultConverter.INSTANCE.groupByLargeAreaNoEntityListToMarketItemListVOList(entities);
//        类目
        fillCategoryName(list);
//        采购成本 - 区间
        SkuAreaWarehouseNoMapVO skuAreaWarehouseNoMapVO = areaSkuQueryService.queryAvailableAreaMap (list.stream ().collect (Collectors.groupingBy (MarketItemByLargeAreaListVO::getItemCode, Collectors.mapping (MarketItemByLargeAreaListVO::getLargeAreaNo, Collectors.toList ()))),list.stream ().map (MarketItemByLargeAreaListVO::getLargeAreaNo).collect (Collectors.toList ()));
        Map<String, Set<Integer>> availableAreaMap = skuAreaWarehouseNoMapVO.getSkuAreaNoMap ();
        Map<String, Integer> skuAreaWarehouseNoMap = skuAreaWarehouseNoMapVO.getSku_AreaWarehouseNoMap ();
        Map<Integer, Set<Integer>> largeAreaAreaNoMap = skuAreaWarehouseNoMapVO.getLargeAreaAreaNoMap ();
        fillCost(list,availableAreaMap,skuAreaWarehouseNoMap,largeAreaAreaNoMap);
//        商城售价 - 区间
        fillPrice(list);
//        可用库存
        fillAvailableStock(list,skuAreaWarehouseNoMap,largeAreaAreaNoMap);

        pageInfo.setList (list);
        return pageInfo;
    }

    @Override
    public PageInfo<InventoryBaseVO> pageQueryInventory(InventoryBaseQueryInput queryInput) {
        PageInfo<InventoryBaseVO> result = new PageInfo<>();
        InventoryQueryParam inventoryQueryParam = InventoryConverter.convert(queryInput);
        PageInfo<InventoryEntity> pageInfo = inventoryQueryRepository.pageQueryInventory(inventoryQueryParam);
        if (Objects.isNull(pageInfo) || CollectionUtil.isEmpty(pageInfo.getList())) {
            return new PageInfo<>(Lists.newArrayList());
        }
        // 自定义属性信息
        List<String> skuList = pageInfo.getList().stream().map(InventoryEntity::getSku).distinct().collect(Collectors.toList());
        List<ProductsPropertyValueEntity> skuPropertyValueEntityList = productsPropertyValueQueryRepository.selectSaleValueBySkuList(skuList);
        Map<String, List<ProductsPropertyValueEntity>> valueMap = skuPropertyValueEntityList.stream().collect(Collectors.groupingBy(ProductsPropertyValueEntity::getSku));

        // admin信息
        List<Long> adminIds = pageInfo.getList().stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCreator()))
                .map(item -> item.getCreator().longValue()).distinct().collect(Collectors.toList());
        Map<Long, AdminEntity> adminMap = adminDomainService.getAdminMap(adminIds);

        List<InventoryBaseVO> dataList = Lists.newArrayList();
        pageInfo.getList().forEach(inventoryEntity -> {
            InventoryBaseVO inventoryBaseVO = InventoryConverter.convertInventoryBaseVO(inventoryEntity);
            if (Objects.isNull(inventoryBaseVO)) {
                return;
            }
            inventoryBaseVO.setGoodsCategoryVO(CategoryLevelVO.builder()
                    .secondCategoryName(inventoryEntity.getSecondCategoryName())
                    .build());
            List<ProductsPropertyValueEntity> valueEntityList = valueMap.get(inventoryEntity.getSku());
            inventoryBaseVO.setProductsPropertyValueVOList(ProductResultConverter.toProductsPropertyValueVOList(valueEntityList));
            // 创建人名称
            try {
                inventoryBaseVO.setCreatorName(adminMap.get(inventoryEntity.getCreator().longValue()).getRealname());
            } catch (Exception e) {
                log.warn("商品创建人信息维护有误，adminMap:{}, inventoryEntity.getCreator():{}",JSONObject.toJSONString(adminMap), inventoryEntity.getCreator());
            }
            dataList.add(inventoryBaseVO);
        });
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setList(dataList);
        return result;
    }

    @Override
    public PageInfo<PendingAssociationProductVO> pagePendingAssociationProduct(PendingAssociationProductQueryInput input) {
        PendingAssociationProductQueryParam queryParam = InventoryConverter.convert(input);
        if (Objects.isNull(queryParam)) {
            return new PageInfo<>(Collections.emptyList());
        }
        PageInfo<PendingAssociationProductEntity> pageInfo = inventoryQueryRepository.pagePendingAssociationProduct(queryParam);
        if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageInfo<>(Collections.emptyList());
        }
        // 库位信息
        List<String> skuList = pageInfo.getList().stream().map(PendingAssociationProductEntity::getSku).distinct().collect(Collectors.toList());
        Map<String, String> skuCabinetCodeMap = cabinetInventoryFacade.querySkuCabinetCodeMap(queryParam.getWarehouseNo(), skuList);

        List<PendingAssociationProductVO> voList = pageInfo.getList().stream()
                .map(entity -> PendingAssociationProductConverter.entityToVO(entity, skuCabinetCodeMap))
                .collect(Collectors.toList());

        PageInfo<PendingAssociationProductVO> result = new PageInfo<>(voList);
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setIsLastPage(pageInfo.isIsLastPage());
        return result;
    }

    @Override
    public List<BackCategoryVO> listPendingAssociationCategory(PendingAssociationCategoryQueryInput input) {
        List<CategoryEntity> categoryEntityList = inventoryQueryRepository.listPendingAssociationCategory(InventoryConverter.convert(input));
        // categoryEntityList转换为List<BackCategoryVO>
        return categoryEntityList.stream().map(BackCategoryConverter::convert).collect(Collectors.toList());

    }

    private void fillAvailableStock(List<MarketItemByLargeAreaListVO> list, Map<String, Integer> skuAreaWarehouseNoMap, Map<Integer, Set<Integer>> largeAreaAreaNoMap) {
        if(CollectionUtil.isEmpty (skuAreaWarehouseNoMap)){
            return;
        }
        Map<String, Set<Integer>> collect = new HashMap<> ();
        skuAreaWarehouseNoMap.forEach ((skuarea,warehouseNo)->{
            String[] s = skuarea.split ("_");
            String sku = s[0];
            Set<Integer> warehouseNos = collect.getOrDefault (sku, new HashSet<> ());
            warehouseNos.add (warehouseNo);
            collect.put (sku,warehouseNos);
        });
        // 查询库存信息
        List<WarehouseSkuInventoryDetailResDTO> skuInventoryDetailResDTOS = saleInventoryCenterQueryFacade.queryWarehouseSkuInventoryList(collect);
        Map<String, Integer> storeQuantityMap = skuInventoryDetailResDTOS.stream()
                .collect(Collectors.toMap(
                        item -> item.getWarehouseNo() + "_" + item.getSkuCode(),
                        WarehouseSkuInventoryDetailResDTO::getOnlineQuantity
                ));

        // 补充成本、库存信息
        list.forEach(item -> {
            item.setAvailableStock (0);
            Integer largeAreaNo = item.getLargeAreaNo ();
            Set<Integer> areanos = largeAreaAreaNoMap.get (largeAreaNo);
            if(CollectionUtil.isNotEmpty (areanos)){
                String sku = item.getItemCode ();

                areanos.forEach (areano->{
                    String key = sku + "_" + areano;
                    Integer warehouseNo = skuAreaWarehouseNoMap.getOrDefault (key, null);
                    if(warehouseNo != null){
                        Integer availableStock = storeQuantityMap.get (warehouseNo + "_" + sku);
                        if(availableStock != null) {
                            item.setAvailableStock (availableStock + item.getAvailableStock ());
                            storeQuantityMap.remove (warehouseNo + "_" + sku);//保证sku 在warehouseNo 的维度只加一次
                        }
                    }
                });
            }
        });
    }

    private void fillCost(List<MarketItemByLargeAreaListVO> list, Map<String, Set<Integer>> availableAreaMap, Map<String, Integer> skuAreaWarehouseNoMap, Map<Integer, Set<Integer>> largeAreaAreaNoMap) {
        // 获取成本集合<sku_areano, cost>
        Map<String, ProductCostQueryResp> costQueryRespMap = productCostQueryFacade.selectMapBySkuAndAreaNosAndWarehouseNoMap(availableAreaMap,skuAreaWarehouseNoMap);
        list.forEach (vo->{
            Integer largeAreaNo = vo.getLargeAreaNo ();
            String itemCode = vo.getItemCode ();
            if (largeAreaAreaNoMap.containsKey (largeAreaNo)) {
                Set<Integer> areaNOs = largeAreaAreaNoMap.get (largeAreaNo);
                areaNOs.forEach (areaNo->{
                    if (costQueryRespMap.containsKey (itemCode + "_" + areaNo)) {
                         BigDecimal minCost = vo.getMinCost ();
                         BigDecimal maxCost = vo.getMaxCost ();

                        BigDecimal areaCost = costQueryRespMap.get (itemCode + "_" + areaNo).getCurrentCost ();

                        // 设置 minCost
                        if (minCost == null) {
                            minCost = areaCost; // 如果 minCost 为 null，初始化为 areaCost
                        } else if (areaCost != null && areaCost.compareTo(minCost) < 0) {
                            minCost = areaCost; // 更新 minCost
                        }

                        // 设置 maxCost
                        if (maxCost == null) {
                            maxCost = areaCost; // 如果 maxCost 为 null，初始化为 areaCost
                        } else if (areaCost != null && areaCost.compareTo(maxCost) > 0) {
                            maxCost = areaCost; // 更新 maxCost
                        }

                        // 更新 vo 的最小和最大成本
                        vo.setMinCost(minCost);
                        vo.setMaxCost(maxCost);
                    }
                });
                if(vo.getMinCost () != null && vo.getMaxCost () != null) {
                    if (vo.getMinCost ().compareTo (vo.getMaxCost ()) == 0) {
                        vo.setCost ("¥" + vo.getMinCost ().setScale (2, RoundingMode.HALF_UP));
                    } else {
                        vo.setPrice ("¥" + vo.getMinCost ().setScale (2, RoundingMode.HALF_UP) + "-¥" + vo.getMaxPrice ().setScale (2, RoundingMode.HALF_UP));
                    }
                }
            }
        });
    }

    private void fillPrice(List<MarketItemByLargeAreaListVO> list) {
        list.stream ().filter(e->e.getMinPrice()!=null && e.getMaxPrice()!=null).forEach(e -> {
            if (e.getMinPrice().compareTo (e.getMaxPrice()) == 0) {
                e.setPrice ("¥" + e.getMinPrice ().setScale(2, RoundingMode.HALF_UP));
            }else{
                e.setPrice ("¥" + e.getMinPrice ().setScale(2, RoundingMode.HALF_UP) + "-¥" + e.getMaxPrice ().setScale(2, RoundingMode.HALF_UP));
            }
        });
    }

    private void fillCategoryName(List<MarketItemByLargeAreaListVO> list) {
        List<Long> categoryIdList = list.stream().map(MarketItemByLargeAreaListVO::getCategoryId).distinct().collect(Collectors.toList());
        List<CategoryAllPathEntity> categoryAllPathEntityList = categoryQueryRepository.selectCategoryAllPath(categoryIdList);
        Map<Long, CategoryAllPathEntity> categoryAllPathEntityMap = categoryAllPathEntityList.stream()
                .collect(Collectors.toMap(CategoryAllPathEntity::getCategoryId, Function.identity(), (a, b) -> a));
        list.forEach(item -> {
            CategoryAllPathEntity categoryAllPathEntity = categoryAllPathEntityMap.get(item.getCategoryId());
            if (categoryAllPathEntity != null) {
                item.setAllPathCategoryName (categoryAllPathEntity.getAllPathCategoryName());
            }
        });
    }

}
