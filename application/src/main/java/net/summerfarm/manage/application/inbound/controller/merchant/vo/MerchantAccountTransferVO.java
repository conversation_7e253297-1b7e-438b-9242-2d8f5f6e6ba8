package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-01-10 14:07:22
 * @version 1.0
 *
 */
@Data
public class MerchantAccountTransferVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 主账号 mid
	 */
	private Long mId;

	/**
	 * 主账号 名称
	 */
	private String mname;

	/**
	 * 要迁移的mid
	 */
	private Long transferMId;

	/**
	 * 操作人姓名
	 */
	private String operatorName;

	/**
	 * 运营服务区编号
	 */
	private Long areaNo;

	/**
	 * 运营服务区名称
	 */
	private String areaName;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 地址
	 */
	private String addr;

	/**
	 * 归属bd名称
	 */
	private String bdName;

	/**
	 * 被迁移店铺名称
	 */
	private String transferMname;

	/**
	 * 被迁移的bd名称
	 */
	private String transferBdName;

	/**
	 * 主账号手机号
	 */
	private String phone;

	/**
	 * 被转移的主账号手机号
	 */
	private String transferPhone;



}