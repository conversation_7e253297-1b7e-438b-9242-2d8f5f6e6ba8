package net.summerfarm.manage.application.service.product.mall.impl;

import com.google.common.collect.Lists;
import net.summerfarm.manage.application.inbound.controller.product.handler.ExternalProductHandler;
import net.summerfarm.manage.application.service.product.mall.AppPopBiaoguoTopSaleSkuQueryService;
import net.summerfarm.manage.application.service.product.mall.ExternalProductMappingQueryService;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.ProductPageQueryParam;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoTopSaleSkuQueryParam;
import net.summerfarm.manage.domain.product.repository.*;
import net.summerfarm.manage.facade.goods.PopBuyerFacade;
import net.summerfarm.manage.facade.goods.dto.PopBuyerInfoDTO;
import net.summerfarm.manage.facade.pms.PopSkuAdditionQueryFacade;
import net.summerfarm.manage.facade.pms.dto.PopSkuCostFacadeDTO;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.summerfarm.manage.application.inbound.controller.product.input.ExternalProductMappingQueryInput;
import net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam;
import net.summerfarm.manage.application.inbound.controller.product.assembler.ExternalProductMappingAssembler;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminId;

/**
*
* <AUTHOR>
* @date 2024-11-15 14:13:27
* @version 1.0
*
*/
@Service
public class ExternalProductMappingQueryServiceImpl implements ExternalProductMappingQueryService {

    @Resource
    private ExternalProductMappingQueryRepository externalProductMappingQueryRepository;
    @Resource
    private PopBuyerFacade popBuyerFacade;
    @Resource
    private AppPopBiaoguoTopSaleSkuQueryRepository appPopBiaoguoTopSaleSkuQueryRepository;
    @Resource
    private InventoryQueryRepository inventoryQueryRepository;
    @Resource
    private AreaSkuQueryRepository areaSkuQueryRepository;
    @Resource
    private PopSkuAdditionQueryFacade popSkuAdditionQueryFacade;
    @Resource
    private AppPopBiaoguoProductsDfQueryRepository appPopBiaoguoProductsDfQueryRepository;

    @Override
    public PageInfo<ExternalProductMappingEntity> getPage(ExternalProductMappingQueryInput input) {
        ExternalProductMappingQueryParam queryParam = ExternalProductMappingAssembler.toExternalProductMappingQueryParam(input);
        return externalProductMappingQueryRepository.getPage(queryParam);
    }

    @Override
    public ExternalProductMappingEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return externalProductMappingQueryRepository.selectById(id);
    }

    @Override
    public PageInfo<ExternalProductMappingEntity> getPageUnmapped(ExternalProductMappingQueryInput input) {
        ExternalProductMappingQueryParam queryParam = ExternalProductMappingAssembler.toExternalProductMappingQueryParam(input);
        if (Objects.isNull(queryParam.getBuyerId())) {
            queryParam.setIgnoreMapping(Boolean.TRUE);
        }
        return externalProductMappingQueryRepository.getPageUnmapped(queryParam);
    }

    @Override
    public List<CategoryEntity> getListUnmappedCategory(ExternalProductMappingQueryInput input) {
        ExternalProductMappingQueryParam queryParam = ExternalProductMappingAssembler.toExternalProductMappingQueryParam(input);
        return externalProductMappingQueryRepository.getListUnmappedCategory(queryParam);
    }

    @Override
    public PageInfo<ExternalProductMappingEntity> getPageMapped(ExternalProductMappingQueryInput input) {
        ExternalProductMappingQueryParam queryParam = ExternalProductMappingAssembler.toExternalProductMappingQueryParam(input);
        PageInfo<ExternalProductMappingEntity> pageInfo = externalProductMappingQueryRepository.getPageMapped(queryParam);
        if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageInfo<>(Lists.newArrayList());
        }
        List<String> externalSkuCodeList = pageInfo.getList().stream().map(ExternalProductMappingEntity::getExternalSkuCode).distinct().collect(Collectors.toList());
        AppPopBiaoguoTopSaleSkuQueryParam skuQueryParam = new AppPopBiaoguoTopSaleSkuQueryParam();
        skuQueryParam.setSkuCodeList(externalSkuCodeList);
        List<AppPopBiaoguoTopSaleSkuEntity> topSaleSkuEntityList = appPopBiaoguoTopSaleSkuQueryRepository.selectByCondition(skuQueryParam);
        Map<String, AppPopBiaoguoTopSaleSkuEntity> topSaleSkuEntityMap = topSaleSkuEntityList.stream().collect(Collectors.toMap(AppPopBiaoguoTopSaleSkuEntity::getSkuCode, Function.identity(), (a, b) -> a));
        pageInfo.getList().forEach(externalProductMappingEntity -> {
            if (Objects.isNull(externalProductMappingEntity)) {
                return;
            }
            AppPopBiaoguoTopSaleSkuEntity appPopBiaoguoTopSaleSkuEntity = topSaleSkuEntityMap.get(externalProductMappingEntity.getExternalSkuCode());
            externalProductMappingEntity.setAppPopBiaoguoTopSaleSkuEntity(appPopBiaoguoTopSaleSkuEntity);
        });
        return pageInfo;
    }

    @Override
    public List<CategoryEntity> getListMappedCategory(ExternalProductMappingQueryInput input) {
        ExternalProductMappingQueryParam queryParam = ExternalProductMappingAssembler.toExternalProductMappingQueryParam(input);
        return externalProductMappingQueryRepository.getListMappedCategory(queryParam);
    }

    @Override
    public PageInfo<ExternalProductMappingEntity> pageQueryExternalProductMapping(ExternalProductMappingQueryInput input) {
        ExternalProductMappingQueryParam queryParam = ExternalProductMappingAssembler.toExternalProductMappingQueryParam(input);
        PageInfo<ExternalProductMappingEntity> pageInfo = externalProductMappingQueryRepository.getPageMapped(queryParam);
        if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageInfo<>(Lists.newArrayList());
        }
        // 鲜沐数据填充
        List<String> xmSkuCodeList = pageInfo.getList().stream().map(ExternalProductMappingEntity::getXmSkuCode).distinct().collect(Collectors.toList());
        ProductPageQueryParam param = new ProductPageQueryParam();
        param.setSkuList(xmSkuCodeList);
        List<ProductEntity> productEntityList = inventoryQueryRepository.listProductEntityByQuery(param);
        Map<String, ProductEntity> xmSkuMap = productEntityList.stream().collect(Collectors.toMap(ProductEntity::getSku, Function.identity(), (a, b) -> a));
        // 区域售价
        List<AreaSkuEntity> areaSkuList = areaSkuQueryRepository.queryListSkuPrice(xmSkuCodeList, null, null);
        Map<String, List<AreaSkuEntity>> areaSkuMap = areaSkuList.stream().collect(Collectors.groupingBy(AreaSkuEntity::getSku));
        // 鲜沐成本价
        List<PopSkuCostFacadeDTO> popSkuCostFacadeDTOList = popSkuAdditionQueryFacade.queryPopSkuCost(xmSkuCodeList);
        Map<String, PopSkuCostFacadeDTO> popSkuCostMap = popSkuCostFacadeDTOList.stream()
                .collect(Collectors.toMap(PopSkuCostFacadeDTO::getSku, Function.identity(), (a, b) -> a));

        // 外部品数据填充
        List<String> externalSkuCodeList = pageInfo.getList().stream().map(ExternalProductMappingEntity::getExternalSkuCode).distinct().collect(Collectors.toList());
        AppPopBiaoguoTopSaleSkuQueryParam skuQueryParam = new AppPopBiaoguoTopSaleSkuQueryParam();
        skuQueryParam.setSkuCodeList(externalSkuCodeList);
        skuQueryParam.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        List<AppPopBiaoguoTopSaleSkuEntity> topSaleSkuEntityList = appPopBiaoguoTopSaleSkuQueryRepository.selectByCondition(skuQueryParam);
        Map<String, AppPopBiaoguoTopSaleSkuEntity> topSaleSkuEntityMap = topSaleSkuEntityList.stream().collect(Collectors.toMap(AppPopBiaoguoTopSaleSkuEntity::getSkuCode, Function.identity(), (a, b) -> a));
        // 外部成本数据
        AppPopBiaoguoProductsDfQueryParam productsDfQueryParam = new AppPopBiaoguoProductsDfQueryParam();
        productsDfQueryParam.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        productsDfQueryParam.setSkuCodeList(externalSkuCodeList);
        List<AppPopBiaoguoProductsDfEntity> appPopBiaoguoProductsDfEntityList = appPopBiaoguoProductsDfQueryRepository.selectByCondition(productsDfQueryParam);
        Map<String, AppPopBiaoguoProductsDfEntity> biaoguoProductsDfEntityMap = appPopBiaoguoProductsDfEntityList.stream().collect(Collectors.toMap(AppPopBiaoguoProductsDfEntity::getSkuCode, Function.identity(), (a, b) -> a));

        pageInfo.getList().forEach(externalProductMappingEntity -> {
            if (Objects.isNull(externalProductMappingEntity)) {
                return;
            }
            ProductEntity productEntity = xmSkuMap.get(externalProductMappingEntity.getXmSkuCode());
            if (Objects.isNull(productEntity)) {
                return;
            }
            PopSkuCostFacadeDTO popSkuCostFacadeDTO = popSkuCostMap.get(externalProductMappingEntity.getXmSkuCode());
            AppPopBiaoguoProductsDfEntity appPopBiaoguoProductsDfEntity = biaoguoProductsDfEntityMap.get(externalProductMappingEntity.getExternalSkuCode());

            List<AreaSkuEntity> areaSkuEntityList = areaSkuMap.get(externalProductMappingEntity.getXmSkuCode());
            BigDecimal maxPrice = CollectionUtils.isEmpty(areaSkuEntityList) ? BigDecimal.ZERO : areaSkuEntityList.stream().map(AreaSkuEntity::getPrice).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            BigDecimal minPrice = CollectionUtils.isEmpty(areaSkuEntityList) ? BigDecimal.ZERO : areaSkuEntityList.stream().map(AreaSkuEntity::getPrice).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);

            externalProductMappingEntity.setTitle(productEntity.getProductName());
            externalProductMappingEntity.setSpecification(productEntity.getWeight());
            externalProductMappingEntity.setWeightNum(productEntity.getWeightNum());
            externalProductMappingEntity.setNetWeightNum(productEntity.getNetWeightNum());
            externalProductMappingEntity.setMinPrice(minPrice);
            externalProductMappingEntity.setMaxPrice(maxPrice);
            externalProductMappingEntity.setXmSkuCost(Objects.isNull(popSkuCostFacadeDTO) ? null : popSkuCostFacadeDTO.getSupplierCost());
            externalProductMappingEntity.setXmSkuWeightCost(Objects.isNull(popSkuCostFacadeDTO) ? null : popSkuCostFacadeDTO.getSupplierWeightPrice());
            externalProductMappingEntity.setExternalSkuCost(Objects.isNull(appPopBiaoguoProductsDfEntity) ? null : appPopBiaoguoProductsDfEntity.getSkuCost());
            externalProductMappingEntity.setExternalSkuGrossWeightCost(Objects.isNull(appPopBiaoguoProductsDfEntity) ? null : appPopBiaoguoProductsDfEntity.getSkuGrossWeightCost());
            AppPopBiaoguoTopSaleSkuEntity appPopBiaoguoTopSaleSkuEntity = topSaleSkuEntityMap.get(externalProductMappingEntity.getExternalSkuCode());
            externalProductMappingEntity.setAppPopBiaoguoTopSaleSkuEntity(appPopBiaoguoTopSaleSkuEntity);
            externalProductMappingEntity.setAppPopBiaoguoProductsDfEntity(appPopBiaoguoProductsDfEntity);
        });
        return pageInfo;
    }
}