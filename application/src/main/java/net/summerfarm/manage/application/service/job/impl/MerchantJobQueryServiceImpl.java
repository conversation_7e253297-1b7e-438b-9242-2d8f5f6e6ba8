package net.summerfarm.manage.application.service.job.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.manage.application.converter.job.MerchantJobConverter;
import net.summerfarm.manage.application.inbound.controller.job.vo.JobProductVO;
import net.summerfarm.manage.application.inbound.controller.job.vo.MerchantJobDetailVo;
import net.summerfarm.manage.application.inbound.controller.job.vo.MerchantJobVo;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductPageInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductVO;
import net.summerfarm.manage.application.service.job.MerchantJobQueryService;
import net.summerfarm.manage.application.service.product.mall.ProductQueryService;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.service.AdminDomainService;
import net.summerfarm.manage.domain.coupon.repository.CouponQueryRepository;
import net.summerfarm.manage.domain.job.dto.MerchantJobProgressExcelDTO;
import net.summerfarm.manage.domain.merchantpool.entity.MerchantPoolInfoEntity;
import net.summerfarm.manage.domain.merchantpool.repository.MerchantPoolInfoRepository;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.jobsdk.enums.CrmJobEnum;
import net.xianmu.jobsdk.enums.CrmJobMerchantDetailEnum;
import net.xianmu.jobsdk.enums.DownloadBizTypeEnum;
import net.xianmu.jobsdk.mapper.CrmJobCompletionCriteriaMapper;
import net.xianmu.jobsdk.mapper.CrmJobMapper;
import net.xianmu.jobsdk.mapper.CrmJobMerchantDetailMapper;
import net.xianmu.jobsdk.mapper.CrmJobRewardRecordMapper;
import net.xianmu.jobsdk.model.po.CrmJob;
import net.xianmu.jobsdk.model.po.CrmJobCompletionCriteria;
import net.xianmu.jobsdk.model.po.CrmJobMerchantDetail;
import net.xianmu.jobsdk.model.po.CrmJobRewardRecord;
import net.xianmu.jobsdk.model.query.MerchantJobQuery;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.helper.XianMuOssHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.xianmu.jobsdk.enums.CrmJobEnum.Status.COMPLETED;
import static net.xianmu.jobsdk.enums.CrmJobEnum.Status.IN_PROGRESS;

@Service
@Slf4j
public class MerchantJobQueryServiceImpl implements MerchantJobQueryService {

    @Resource
    private CrmJobMapper crmJobMapper;
    @Resource
    private CrmJobCompletionCriteriaMapper crmJobCompletionCriteriaMapper;
    @Resource
    private CrmJobMerchantDetailMapper crmJobMerchantDetailMapper;
    @Resource
    private MerchantPoolInfoRepository merchantPoolInfoRepository;
    @Resource
    private ProductQueryService productQueryService;
    @Resource
    private CouponQueryRepository couponQueryRepository;
    @Resource
    private CrmJobRewardRecordMapper crmJobRewardRecordMapper;
    @Resource
    private AdminDomainService adminDomainService;

    @Override
    public PageInfo<MerchantJobVo> pageJob(MerchantJobQuery query) {
        PageInfo<CrmJob> crmJobs = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> crmJobMapper.getMerchantJobByQuery(query));
        PageInfo<MerchantJobVo> result = MerchantJobConverter.INSTANCE.poToVo(crmJobs);

        // 填充创建人名字
        fillCreatorName(result.getList());

        return result;
    }

    @Override
    public MerchantJobDetailVo getJobDetail(Long jobId) {
        CrmJob crmJob = crmJobMapper.selectById(jobId);
        if (crmJob == null) {
            throw new ParamsException("任务不存在");
        }

        List<CrmJobCompletionCriteria> completionCriteria = crmJobCompletionCriteriaMapper.selectByJobId(jobId);
        MerchantJobDetailVo jobDetail = MerchantJobConverter.INSTANCE.assembleDetail(crmJob, completionCriteria.get(0));

        // 人群包详情
        if (Objects.equals(CrmJobEnum.MerchantSelectionType.MERCHANT_POOL_INFO_ID_LIST.getCode(), crmJob.getMerchantSelectionType())
                && crmJob.getMerchantSelectionList() != null) {
            List<Long> merchantPoolIds = JSONObject.parseArray(crmJob.getMerchantSelectionList(), Long.class);
            List<MerchantPoolInfoEntity> poolInfo = merchantPoolInfoRepository.selectByIdIn(merchantPoolIds);
            jobDetail.setMerchantPoolList(MerchantJobConverter.INSTANCE.poolEntityToVo(poolInfo));
        }

        // 商品详情 已废弃, 使用分页接口查询
//        List<String> productList = Optional.ofNullable(crmJob.getProductList())
//                .map(json -> JSONObject.parseArray(json, String.class))
//                .orElse(Collections.emptyList());
//        if (CollectionUtil.isNotEmpty(productList)) {
//            ProductPageInput input = new ProductPageInput();
//            input.setSkuList(productList);
//            input.setPageIndex(1);
//            input.setPageSize(productList.size());
//            PageInfo<ProductVO> page = productQueryService.pageByQuery(input);
//            jobDetail.setProductList(MerchantJobConverter.INSTANCE.productVoToJobProductVo(page.getList()));
//        }

        // 卡券名称
        if (StringUtils.isNotBlank(crmJob.getRewardValue())) {
            try {
                jobDetail.setCouponName(couponQueryRepository.selectById(Long.valueOf(jobDetail.getRewardValue())).getName());
            } catch (Exception e) {
                log.info("任务{}的卡券{}不存在", jobId, crmJob.getRewardValue(), e);
            }
        }

        // excel文件的下载地址
        if (StringUtils.isNotBlank(crmJob.getExcelUrl())) {
            String downloadLink = XianMuOssHelper.generateUrl(crmJob.getExcelUrl());
            jobDetail.setExcelDownloadUrl(downloadLink);
        }

        return jobDetail;
    }

    @Override
    public PageInfo<JobProductVO> pageProduct(Long jobId, int pageIndex, int pageSize) {
        CrmJob crmJob = crmJobMapper.selectById(jobId);
        if (crmJob == null) {
            throw new ParamsException("任务不存在");
        }

        List<String> productList = Optional.ofNullable(crmJob.getProductList())
                .map(json -> JSONObject.parseArray(json, String.class))
                .orElse(Collections.emptyList());
        if (CollectionUtil.isEmpty(productList)) {
            return new PageInfo<>(Collections.emptyList());
        }

        // 分页查询商品
        // 手写分页, 直接依赖queryService的分页可能出现query里塞进几万个sku的情况
        int total = productList.size();
        int start = (pageIndex - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        List<String> subSkuIds = productList.subList(start, end);
        ProductPageInput input = new ProductPageInput();
        input.setSkuList(subSkuIds);
        input.setPageIndex(1);
        input.setPageSize(subSkuIds.size());
        List<ProductVO> list = productQueryService.pageByQuery(input).getList();

        // 封装分页数据
        Page<JobProductVO> page = new Page<>(pageIndex, pageSize);
        if (CollectionUtil.isNotEmpty(list)) {
            page.addAll(MerchantJobConverter.INSTANCE.productVoToJobProductVo(list));
        }
        page.setPages((total + pageSize - 1) / pageSize);
        page.setTotal(total);
        return new PageInfo<>(page);
    }

    @Override
    public Long exportJobProgress(Long jobId, Long adminId) {
        CrmJob crmJob = crmJobMapper.selectById(jobId);

        // 初始校验
        if (crmJob == null) {
            throw new ParamsException("任务不存在");
        }
        CrmJobEnum.Status status = CrmJobEnum.Status.getByCode(crmJob.getStatus());
        if (status != IN_PROGRESS && status != COMPLETED) {
            throw new BizException(String.format("任务%s的状态不合法,仅支持导出进行中和已完成任务的进度", jobId));
        }

        // 上传至下载中心
        DownloadCenterRecordDTO record = new DownloadCenterRecordDTO();
        record.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        record.setUserId(adminId);
        record.setBizType(DownloadBizTypeEnum.MERCHANT_JOB_PROGRESS.getBizType());
        record.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        record.setFileName(String.format("任务%s进度.xlsx", jobId));
        record.setParams("{\"任务id\":" + jobId + "}");
        return DownloadCenterHelper.build(record).asyncWrite(record, p -> {
            // 获取任务详情
            List<CrmJobMerchantDetail> details = crmJobMerchantDetailMapper.selectByJobId(jobId);
            if (CollectionUtil.isEmpty(details)) {
                return Collections.emptyList();
            }
            Map<Long, CrmJobRewardRecord> mIdRewardMap = crmJobRewardRecordMapper.selectByJobId(jobId).stream()
                    .collect(Collectors.toMap(CrmJobRewardRecord::getMId, Function.identity()));
            return details.stream().map(detail -> {
                MerchantJobProgressExcelDTO dto = new MerchantJobProgressExcelDTO();
                dto.setMId(detail.getMId());
                List<String> skus = JSONObject.parseArray(detail.getMerchantProductList(), String.class);
                dto.setSku(CollectionUtil.isEmpty(skus) ? "" : String.join(",", skus));
                dto.setClaimingStatus(CrmJobMerchantDetailEnum.TaskClaimingStatus.getByCode(detail.getClaimingStatus()).getDescription());

                CrmJobMerchantDetailEnum.TaskCompletionStatus completionStatus = CrmJobMerchantDetailEnum.TaskCompletionStatus.getByCode(detail.getStatus());
                dto.setStatus(completionStatus.getDescription());

                // 已完成但未领取奖励,订单号是detail orderNo.get(0)
                if (completionStatus == CrmJobMerchantDetailEnum.TaskCompletionStatus.COMPLETED_UNAWARDED) {
                    List<String> orderNos = JSONObject.parseArray(detail.getOrderNoList(), String.class);
                    dto.setCompletionOrderNo(CollectionUtil.isEmpty(orderNos) ? "" : orderNos.get(0));
                }
                // 已领取奖励,订单号是rewardRecord orderNo
                else if (completionStatus == CrmJobMerchantDetailEnum.TaskCompletionStatus.AWARD_ISSUED) {
                    CrmJobRewardRecord rewardRecord = mIdRewardMap.get(detail.getMId());
                    List<String> orderNos = JSONObject.parseArray(rewardRecord.getOrderNoList(), String.class);
                    dto.setCompletionOrderNo(CollectionUtil.isEmpty(orderNos) ? "" : orderNos.get(0));
                }
                return dto;
            }).collect(Collectors.toList());
        }, MerchantJobProgressExcelDTO.class);
    }

    /**
     * 填充创建人名字
     *
     * @param merchantJobVos 任务VO列表
     */
    private void fillCreatorName(List<MerchantJobVo> merchantJobVos) {
        if (CollectionUtil.isEmpty(merchantJobVos)) {
            return;
        }

        // 提取所有创建人ID
        List<Long> creatorIds = merchantJobVos.stream()
                .map(MerchantJobVo::getCreator)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(creatorIds)) {
            return;
        }

        // 批量获取管理员信息
        Map<Long, AdminEntity> adminMap = adminDomainService.getAdminMap(creatorIds);

        // 填充创建人名字
        merchantJobVos.forEach(vo -> {
            if (vo.getCreator() != null) {
                AdminEntity admin = adminMap.get(vo.getCreator());
                if (admin != null) {
                    vo.setCreatorName(admin.getRealname());
                }
            }
        });
    }
}
