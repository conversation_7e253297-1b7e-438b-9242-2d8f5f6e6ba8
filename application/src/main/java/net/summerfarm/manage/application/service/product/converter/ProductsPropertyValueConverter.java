package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.input.ProductsPropertyValueInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductsPropertyValueVO;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyValueCommandParam;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyValueQueryParam;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminName;

/**
 * @ClassName ProductsPropertyValueConverter
 * @Description
 * <AUTHOR>
 * @Date 18:09 2024/4/30
 * @Version 1.0
 **/
public class ProductsPropertyValueConverter {


    public static List<ProductsPropertyValueQueryParam> inputToParam(List<ProductsPropertyValueInput> saleValueList) {
        if (CollectionUtils.isEmpty(saleValueList)) {
            return Collections.emptyList();
        }

        List<ProductsPropertyValueQueryParam> params = new ArrayList<>(saleValueList.size());
        for (ProductsPropertyValueInput productsPropertyValueInput : saleValueList) {
            ProductsPropertyValueQueryParam param = new ProductsPropertyValueQueryParam();
            param.setProductsPropertyValue(productsPropertyValueInput.getProductsPropertyValue());
            param.setProductsPropertyId(productsPropertyValueInput.getProductsPropertyId());
            params.add(param);
        }
        return params;
    }

    public static List<ProductsPropertyValueCommandParam> inputToCommonParam(List<ProductsPropertyValueInput> saleValueList) {
        if (CollectionUtils.isEmpty(saleValueList)) {
            return Collections.emptyList();
        }

        List<ProductsPropertyValueCommandParam> params = new ArrayList<>(saleValueList.size());
        for (ProductsPropertyValueInput productsPropertyValueInput : saleValueList) {
            ProductsPropertyValueCommandParam param = new ProductsPropertyValueCommandParam();
            param.setProductsPropertyValue(productsPropertyValueInput.getProductsPropertyValue());
            param.setProductsPropertyId(productsPropertyValueInput.getProductsPropertyId());
            param.setPdId(productsPropertyValueInput.getPdId());
            param.setSku(productsPropertyValueInput.getSku());
            param.setCreator(getAdminName());
            params.add(param);
        }
        return params;
    }

    public static List<ProductsPropertyValueVO> entityToVO(List<ProductsPropertyValueEntity> productsPropertyValueEntities) {
        if (CollectionUtils.isEmpty(productsPropertyValueEntities)) {
            return Collections.emptyList();
        }

        List<ProductsPropertyValueVO> vos = new ArrayList<>(productsPropertyValueEntities.size());
        for (ProductsPropertyValueEntity productsPropertyValueEntity : productsPropertyValueEntities) {
            ProductsPropertyValueVO productsPropertyValueVO = new ProductsPropertyValueVO();
            productsPropertyValueVO.setProductsPropertyId(productsPropertyValueEntity.getProductsPropertyId());
            productsPropertyValueVO.setProductsPropertyValue(productsPropertyValueEntity.getProductsPropertyValue());
            productsPropertyValueVO.setId(productsPropertyValueEntity.getId());
            productsPropertyValueVO.setPdId(productsPropertyValueEntity.getPdId());
            productsPropertyValueVO.setName(productsPropertyValueEntity.getName());
            productsPropertyValueVO.setSku(productsPropertyValueEntity.getSku());
            productsPropertyValueVO.setFormatStr(productsPropertyValueEntity.getFormatStr());
            productsPropertyValueVO.setFormatType(productsPropertyValueEntity.getFormatType());
            productsPropertyValueVO.setType(productsPropertyValueEntity.getType());
            vos.add(productsPropertyValueVO);
        }
        return vos;
    }
}
