package net.summerfarm.manage.application.inbound.controller.contact.assembler;

import net.summerfarm.manage.application.inbound.controller.contact.vo.ContactVO;
import net.summerfarm.manage.facade.deliivery.dto.ContactDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/11/14 16:09:07
 */
public class ContactAssembler {

    public static ContactVO toContactVO(ContactDTO contactDTO) {
        if (contactDTO == null) {
            return null;
        }
        ContactVO contactVO = new ContactVO();
        contactVO.setId(contactDTO.getContactId());
        contactVO.setFrequentMethod(contactDTO.getFrequentMethod());
        contactVO.setBeginCalculateDate(contactDTO.getBeginCalculateDate());
        contactVO.setWeekDeliveryFrequent(contactDTO.getWeekDeliveryFrequent());
        contactVO.setDeliveryFrequentInterval(contactDTO.getDeliveryFrequentInterval());
        return contactVO;
    }
}
