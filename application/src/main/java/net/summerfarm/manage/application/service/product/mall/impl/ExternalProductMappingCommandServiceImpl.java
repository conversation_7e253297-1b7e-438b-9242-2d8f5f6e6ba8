package net.summerfarm.manage.application.service.product.mall.impl;


import net.summerfarm.manage.application.service.product.validator.InventoryValidator;
import net.summerfarm.manage.common.enums.products.ExternalProductMappingEnum;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam;
import net.summerfarm.manage.domain.product.repository.ExternalProductMappingQueryRepository;
import net.summerfarm.util.ExceptionUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.summerfarm.manage.application.service.product.mall.ExternalProductMappingCommandService;
import net.summerfarm.manage.domain.product.service.ExternalProductMappingCommandDomainService;
import net.summerfarm.manage.application.inbound.controller.product.input.ExternalProductMappingCommandInput;
import net.summerfarm.manage.domain.product.param.command.ExternalProductMappingCommandParam;
import net.summerfarm.manage.application.inbound.controller.product.assembler.ExternalProductMappingAssembler;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2024-11-15 14:13:27
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class ExternalProductMappingCommandServiceImpl implements ExternalProductMappingCommandService {

    @Resource
    private ExternalProductMappingCommandDomainService externalProductMappingCommandDomainService;
    @Resource
    private InventoryValidator inventoryValidator;
    @Resource
    private ExternalProductMappingQueryRepository externalProductMappingQueryRepository;


    @Override
    public ExternalProductMappingEntity insert(ExternalProductMappingCommandInput input) {
        // pop sku存在性校验
        inventoryValidator.checkPopSkuExist(input.getXmSkuCode());
        // 唯一性校验
        ExternalProductMappingQueryParam queryParam = new ExternalProductMappingQueryParam();
        queryParam.setType(ExternalProductMappingEnum.SKU.getValue());
        queryParam.setInternalValue(input.getXmSkuCode());
        queryParam.setExternalValue(input.getExternalSkuCode());
        List<ExternalProductMappingEntity> externalProductMappingEntityList = externalProductMappingQueryRepository.selectByCondition(queryParam);
        if (CollectionUtils.isNotEmpty(externalProductMappingEntityList)
                && Objects.nonNull(externalProductMappingEntityList.get(0))) {
            return externalProductMappingEntityList.get(0);
        }
        ExternalProductMappingCommandParam param = ExternalProductMappingAssembler.buildCreateParam(input);
        return externalProductMappingCommandDomainService.insert(param);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ExternalProductMappingCommandInput input) {
        // 校验绑定关系
        ExternalProductMappingQueryParam queryParam = new ExternalProductMappingQueryParam();
        queryParam.setType(ExternalProductMappingEnum.SKU.getValue());
        queryParam.setInternalValue(input.getXmSkuCode());
        List<ExternalProductMappingEntity> externalProductMappingEntityList = externalProductMappingQueryRepository.selectByCondition(queryParam);
        ExceptionUtil.checkAndThrow(CollectionUtils.isNotEmpty(externalProductMappingEntityList)
                && Objects.nonNull(externalProductMappingEntityList.get(0)), "未查询到需要绑定的鲜沐商品信息，请刷新页面后重试");
        ExternalProductMappingEntity externalProductMappingEntity = externalProductMappingEntityList.get(0);
        // 删除原有绑定关系
        this.delete(externalProductMappingEntity.getId());
        // 重新建立绑定关系
        ExternalProductMappingCommandInput commandInput = ExternalProductMappingCommandInput.builder()
                .type(ExternalProductMappingEnum.SKU.getValue())
                .xmSkuCode(input.getXmSkuCode())
                .externalSkuCode(input.getExternalSkuCode()).build();
        this.insert(commandInput);
    }


    @Override
    public int delete(Long id) {
        return externalProductMappingCommandDomainService.delete(id);
    }
}