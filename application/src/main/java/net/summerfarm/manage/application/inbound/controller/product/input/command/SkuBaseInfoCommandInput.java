package net.summerfarm.manage.application.inbound.controller.product.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * @Description
 * @Date 2025/4/15 15:56
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBaseInfoCommandInput {

    /**
     * sku编码
     */
    @NotBlank(message = "sku不能为空")
    private String sku;

    /**
     * 体积
     */
    private String volume;

    /**
     * 重量kg
     */
    private BigDecimal weightNum;


}
