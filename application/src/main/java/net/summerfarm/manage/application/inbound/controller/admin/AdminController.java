package net.summerfarm.manage.application.inbound.controller.admin;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.admin.assembler.AdminAssembler;
import net.summerfarm.manage.application.inbound.controller.admin.input.query.AdminQueryInput;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminVO;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminWithDataPermissionVO;
import net.summerfarm.manage.application.service.admin.AdminQueryService;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @Title 用户相关的接口
 * @Description 核心管理员表功能模块
 * @date 2024-06-18 13:21:08
 */
@RestController
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private AdminQueryService adminQueryService;


    /**
     * 管理员列表接口(暂未实现:2024-06-18)
     *
     * @return AdminVO
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<AdminVO>> getPage(@RequestBody AdminQueryInput input) {
        PageInfo<AdminEntity> page = adminQueryService.getPage(input);
        return CommonResult.ok(PageInfoConverter.toPageResp(page, AdminAssembler::toAdminVO));
    }

    /**
     * 获取当前登录用户的详情
     *
     * @return AdminVO
     */
    @RequestMapping(path = "/query/personalInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public CommonResult<AdminWithDataPermissionVO> detail() {
        return CommonResult.ok(adminQueryService.getCurrentUserDetail());
    }
}

