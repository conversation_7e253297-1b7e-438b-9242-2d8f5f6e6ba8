package net.summerfarm.manage.application.inbound.provider.aftersale.converter;

import net.summerfarm.client.resp.aftersale.AfterSaleDeliveryPathInfoResp;
import net.summerfarm.manage.domain.afterSale.flatObject.AfterSaleDeliveryPathFlatObject;

/**
 * Description: 售后转换类<br/>
 * date: 2025/1/2 13:52<br/>
 *
 * <AUTHOR> />
 */
public class AfterSaleOrderConverter {

    public static AfterSaleDeliveryPathInfoResp afterSaleDeliveryPathFlatObjectToResp(AfterSaleDeliveryPathFlatObject flatObject) {
        if(flatObject == null){
            return null;
        }
        AfterSaleDeliveryPathInfoResp resp = new AfterSaleDeliveryPathInfoResp();

        resp.setId(flatObject.getId());
        resp.setAfterSaleNo(flatObject.getAfterSaleNo());
        resp.setStoreNo(flatObject.getStoreNo());
        resp.setMname(flatObject.getMname());
        resp.setMsize(flatObject.getMsize());
        resp.setMId(flatObject.getMId());
        resp.setContactName(flatObject.getContactName());
        resp.setDeliveryTime(flatObject.getDeliveryTime());
        resp.setDeliveryType(flatObject.getDeliveryType());
        resp.setOrderItemDeliveryType(flatObject.getOrderItemDeliveryType());
        resp.setContactPhone(flatObject.getContactPhone());
        resp.setContactAddress(flatObject.getContactAddress());
        resp.setPdName(flatObject.getPdName());
        resp.setQuantity(flatObject.getQuantity());
        resp.setWeight(flatObject.getWeight());
        resp.setSku(flatObject.getSku());
        resp.setContactId(flatObject.getContactId());
        resp.setBrandName(flatObject.getBrandName());
        resp.setBigCustomerId(flatObject.getBigCustomerId());

        return resp;
    }
}
