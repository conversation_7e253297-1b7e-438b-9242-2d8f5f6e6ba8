package net.summerfarm.manage.application.service.product.mall;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.product.input.*;
import net.summerfarm.manage.application.inbound.controller.product.input.command.SkuBaseInfoCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ItemLabelVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.MarketItemListVO;
import net.summerfarm.manage.application.service.product.command.PopSkuUpdateCommand;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;

import java.util.List;

/**
 * @ClassName InventoryCommandService
 * @Description
 * <AUTHOR>
 * @Date 11:11 2024/5/6
 * @Version 1.0
 **/
public interface InventoryCommandService {
    /***
     * @author: lzh
     * @description: sku编辑
     * @date: 2024/4/30 16:40
     * @param: [input]
     * @return: void
     **/
    void updateSkuInfo(InventoryUpdateInput input);

    void updateWeight(InventoryEntity inventory, String weight);

    /***
     * @author: lzh
     * @description: 商品标签列表--商品中心获取
     * @date: 2024/5/8 10:33
     * @param: []
     * @return: java.util.List<net.summerfarm.manage.application.inbound.controller.product.vo.ItemLabelVO>
     *
     * @param input*/
    List<ItemLabelVO> allItemLabel(ItemLabelQueryInput input);

    /***
     * @author: lzh
     * @description: 商品标签新增
     * @date: 2024/5/8 10:40
     * @param: [input]
     * @return: net.summerfarm.manage.application.inbound.controller.product.vo.ItemLabelVO
     **/
    ItemLabelVO insertItemLabel(ItemLabelInput input);

    void updateSkuVideo(InventoryVideoInput input);

    /**
     * 分页查询sku
     * @param input
     * @return
     */
    PageInfo<MarketItemListVO> pageSku(MarketBaseQueryInput input);

    /**
     * 更新pop商品信息
     *
     * <AUTHOR>
     * @date 2024/7/30 11:44
     * @param updateCommand 更新参数
     */
    void updatePopSkuInfo(PopSkuUpdateCommand updateCommand);

    /**
     * 更新sku基础信息
     *
     * <AUTHOR>
     * @date 2025/4/15 16:41
     */
    void updateSkuBaseInfo(SkuBaseInfoCommandInput input);
}
