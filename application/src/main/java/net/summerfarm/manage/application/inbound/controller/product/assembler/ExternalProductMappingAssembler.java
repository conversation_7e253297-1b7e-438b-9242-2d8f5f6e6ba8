package net.summerfarm.manage.application.inbound.controller.product.assembler;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.product.vo.BackCategoryVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.ExternalPopProductMappingDetailVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.ExternalPopProductMappingVO;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity;
import net.summerfarm.manage.domain.product.entity.CategoryEntity;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.command.ExternalProductMappingCommandParam;
import net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam;
import net.summerfarm.manage.application.inbound.controller.product.input.ExternalProductMappingQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ExternalProductMappingCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ExternalProductMappingVO;

import java.math.BigDecimal;
import java.util.*;
import java.util.List;

import static net.summerfarm.manage.application.inbound.controller.product.assembler.AppPopBiaoguoTopSaleSkuAssembler.toAppPopBiaoguoTopSaleSkuVO;

/**
 *
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
@Slf4j
public class ExternalProductMappingAssembler {

    private ExternalProductMappingAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static ExternalProductMappingQueryParam toExternalProductMappingQueryParam(ExternalProductMappingQueryInput externalProductMappingQueryInput) {
        if (externalProductMappingQueryInput == null) {
            return null;
        }
        ExternalProductMappingQueryParam externalProductMappingQueryParam = new ExternalProductMappingQueryParam();
        externalProductMappingQueryParam.setPageIndex(externalProductMappingQueryInput.getPageIndex());
        externalProductMappingQueryParam.setPageSize(externalProductMappingQueryInput.getPageSize());
        externalProductMappingQueryParam.setBuyerId(externalProductMappingQueryInput.getBuyerId());
        externalProductMappingQueryParam.setCategoryId(externalProductMappingQueryInput.getCategoryId());
        externalProductMappingQueryParam.setTitle(externalProductMappingQueryInput.getTitle());
        externalProductMappingQueryParam.setInternalValue(externalProductMappingQueryInput.getXmSkuCode());
        return externalProductMappingQueryParam;
    }





    public static ExternalProductMappingCommandParam buildCreateParam(ExternalProductMappingCommandInput externalProductMappingCommandInput) {
        if (externalProductMappingCommandInput == null) {
            return null;
        }
        ExternalProductMappingCommandParam externalProductMappingCommandParam = new ExternalProductMappingCommandParam();
        externalProductMappingCommandParam.setId(externalProductMappingCommandInput.getId());
        externalProductMappingCommandParam.setType(externalProductMappingCommandInput.getType());
        externalProductMappingCommandParam.setInternalValue(externalProductMappingCommandInput.getXmSkuCode());
        externalProductMappingCommandParam.setExternalValue(externalProductMappingCommandInput.getExternalSkuCode());
        return externalProductMappingCommandParam;
    }


    public static ExternalProductMappingCommandParam buildUpdateParam(ExternalProductMappingCommandInput externalProductMappingCommandInput) {
        if (externalProductMappingCommandInput == null) {
            return null;
        }
        ExternalProductMappingCommandParam externalProductMappingCommandParam = new ExternalProductMappingCommandParam();
        externalProductMappingCommandParam.setId(externalProductMappingCommandInput.getId());
        externalProductMappingCommandParam.setCreateTime(externalProductMappingCommandInput.getCreateTime());
        externalProductMappingCommandParam.setUpdateTime(externalProductMappingCommandInput.getUpdateTime());
        externalProductMappingCommandParam.setType(externalProductMappingCommandInput.getType());
        externalProductMappingCommandParam.setInternalValue(externalProductMappingCommandInput.getInternalValue());
        externalProductMappingCommandParam.setExternalValue(externalProductMappingCommandInput.getExternalValue());
        return externalProductMappingCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<ExternalProductMappingVO> toExternalProductMappingVOList(List<ExternalProductMappingEntity> externalProductMappingEntityList) {
        if (externalProductMappingEntityList == null) {
            return Collections.emptyList();
        }
        List<ExternalProductMappingVO> externalProductMappingVOList = new ArrayList<>();
        for (ExternalProductMappingEntity externalProductMappingEntity : externalProductMappingEntityList) {
            externalProductMappingVOList.add(toExternalProductMappingVO(externalProductMappingEntity));
        }
        return externalProductMappingVOList;
}


   public static ExternalProductMappingVO toExternalProductMappingVO(ExternalProductMappingEntity externalProductMappingEntity) {
       if (externalProductMappingEntity == null) {
            return null;
       }
       ExternalProductMappingVO externalProductMappingVO = new ExternalProductMappingVO();
       externalProductMappingVO.setId(externalProductMappingEntity.getId());
       externalProductMappingVO.setCreateTime(externalProductMappingEntity.getCreateTime());
       externalProductMappingVO.setUpdateTime(externalProductMappingEntity.getUpdateTime());
       externalProductMappingVO.setType(externalProductMappingEntity.getType());
       externalProductMappingVO.setInternalValue(externalProductMappingEntity.getInternalValue());
       externalProductMappingVO.setExternalValue(externalProductMappingEntity.getExternalValue());
       return externalProductMappingVO;
   }

    public static ExternalPopProductMappingVO convert(ExternalProductMappingEntity externalProductMappingEntity) {
        if (externalProductMappingEntity == null) {
            return null;
        }
        ExternalPopProductMappingVO externalPopProductMappingVO = new ExternalPopProductMappingVO();
        externalPopProductMappingVO.setId(externalProductMappingEntity.getId());
        externalPopProductMappingVO.setXmSkuCode(externalProductMappingEntity.getXmSkuCode());
        externalPopProductMappingVO.setTitle(externalProductMappingEntity.getTitle());
        externalPopProductMappingVO.setSpecification(externalProductMappingEntity.getSpecification());
        externalPopProductMappingVO.setWeightNum(externalProductMappingEntity.getWeightNum());
        externalPopProductMappingVO.setNetWeightNum(externalProductMappingEntity.getNetWeightNum());
        externalPopProductMappingVO.setMainPicture(externalProductMappingEntity.getMainPicture());
        externalPopProductMappingVO.setCategoryId(externalProductMappingEntity.getCategoryId());
        externalPopProductMappingVO.setCategoryName(externalProductMappingEntity.getCategoryName());
        externalPopProductMappingVO.setPopBiaoguoTopSaleSkuVO(toAppPopBiaoguoTopSaleSkuVO(externalProductMappingEntity.getAppPopBiaoguoTopSaleSkuEntity()));
        return externalPopProductMappingVO;
    }

    public static ExternalPopProductMappingDetailVO convertDetail(ExternalProductMappingEntity externalProductMappingEntity) {
        if (externalProductMappingEntity == null) {
            return null;
        }
        AppPopBiaoguoTopSaleSkuEntity topSaleSkuEntity = externalProductMappingEntity.getAppPopBiaoguoTopSaleSkuEntity();
        AppPopBiaoguoProductsDfEntity appPopBiaoguoProductsDfEntity = externalProductMappingEntity.getAppPopBiaoguoProductsDfEntity();
        ExternalPopProductMappingDetailVO externalPopProductMappingVO = new ExternalPopProductMappingDetailVO();
        externalPopProductMappingVO.setId(externalProductMappingEntity.getId());
        externalPopProductMappingVO.setXmSkuCode(externalProductMappingEntity.getXmSkuCode());
        externalPopProductMappingVO.setTitle(externalProductMappingEntity.getTitle());
        externalPopProductMappingVO.setSpecification(externalProductMappingEntity.getSpecification());
        externalPopProductMappingVO.setCategoryId(externalProductMappingEntity.getCategoryId());
        externalPopProductMappingVO.setCategoryName(externalProductMappingEntity.getCategoryName());
        externalPopProductMappingVO.setWeightNum(externalProductMappingEntity.getWeightNum());
        externalPopProductMappingVO.setNetWeightNum(externalProductMappingEntity.getNetWeightNum());
        externalPopProductMappingVO.setWeightNumJin(Objects.isNull(externalProductMappingEntity.getWeightNum()) ? BigDecimal.ZERO : externalProductMappingEntity.getWeightNum().multiply(BigDecimal.valueOf(2)));
        externalPopProductMappingVO.setNetWeightNumJin(Objects.isNull(externalProductMappingEntity.getNetWeightNum()) ? BigDecimal.ZERO : externalProductMappingEntity.getNetWeightNum().multiply(BigDecimal.valueOf(2)));
        externalPopProductMappingVO.setMaxPrice(externalProductMappingEntity.getMaxPrice());
        externalPopProductMappingVO.setMinPrice(externalProductMappingEntity.getMinPrice());
        externalPopProductMappingVO.setExternalSkuCode(externalProductMappingEntity.getExternalSkuCode());
        externalPopProductMappingVO.setExternalGoodsName(Objects.isNull(appPopBiaoguoProductsDfEntity) ? "" : appPopBiaoguoProductsDfEntity.getGoodsName());
        externalPopProductMappingVO.setExternalSpecification(Objects.isNull(appPopBiaoguoProductsDfEntity) ? "" : appPopBiaoguoProductsDfEntity.getSpecification());
        externalPopProductMappingVO.setExternalCategoryName(Objects.isNull(appPopBiaoguoProductsDfEntity) ? "" : appPopBiaoguoProductsDfEntity.getCategoryName());
        externalPopProductMappingVO.setGrossWeight(Objects.isNull(appPopBiaoguoProductsDfEntity) ? "" : appPopBiaoguoProductsDfEntity.getGrossWeight());
        externalPopProductMappingVO.setNetWeight(Objects.isNull(appPopBiaoguoProductsDfEntity) ? "" : appPopBiaoguoProductsDfEntity.getNetWeight());
        Double finalStandardPrice = 0d;
        try {
            finalStandardPrice = Objects.isNull(appPopBiaoguoProductsDfEntity) ? 0 : Double.parseDouble(appPopBiaoguoProductsDfEntity.getFinalStandardPrice());
        } catch (Exception e) {
            log.warn("最终标准价解析失败 appPopBiaoguoProductsDfEntity:{}", JSON.toJSONString(appPopBiaoguoProductsDfEntity), e);
        }
        externalPopProductMappingVO.setFinalStandardPrice(finalStandardPrice);
        externalPopProductMappingVO.setExternalMonthSaleGmv(Objects.isNull(topSaleSkuEntity) ? 0 : topSaleSkuEntity.getMonthsaleGmv());
        externalPopProductMappingVO.setMonthSale(Objects.isNull(topSaleSkuEntity) ? 0 : topSaleSkuEntity.getMonthSale());
        externalPopProductMappingVO.setXmSkuCost(externalProductMappingEntity.getXmSkuCost());
        externalPopProductMappingVO.setXmSkuWeightCost(externalProductMappingEntity.getXmSkuWeightCost());
        externalPopProductMappingVO.setExternalSkuCost(externalProductMappingEntity.getExternalSkuCost());
        externalPopProductMappingVO.setExternalSkuGrossWeightCost(externalProductMappingEntity.getExternalSkuGrossWeightCost());
        return externalPopProductMappingVO;
    }

    public static BackCategoryVO convert(CategoryEntity categoryEntity) {
        if (Objects.isNull(categoryEntity)) {
            return null;
        }
        return BackCategoryVO.builder()
                .id(categoryEntity.getId())
                .category(categoryEntity.getCategory())
                .parentId(categoryEntity.getParentId())
                .type(categoryEntity.getType())
                .build();
    }


}
