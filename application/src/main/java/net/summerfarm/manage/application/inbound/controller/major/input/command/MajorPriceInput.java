package net.summerfarm.manage.application.inbound.controller.major.input.command;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MajorPriceInput implements Serializable{
    /**
     * sku编号
     */
    @NotNull(groups = {Add.class, Update.class} , message = "sku is not null")
    private String sku;
    /**
     * 状态0=保存,1=提交
     */
    @NotNull(groups = {Add.class, Update.class} , message = "status is not null")
    private Integer status;
    /**
     * 运营大区
     */
    @NotNull(groups = {Add.class, Update.class} , message = "largeAreaNo is not null")
    private Integer largeAreaNo;
    /**
     * 大客户账号id
     */
    @NotNull(groups = {Add.class, Update.class} , message = "adminId is not null")
    private Integer adminId;
    /**
     * 1是账期 2是现结
     */
    @NotNull(groups = {Add.class, Update.class} , message = "direct is not null")
    private Integer direct;
    /**
     * 报价单的生效时间
     */
    @NotNull(groups = {Add.class, Update.class} , message = "validTime is not null")
    private LocalDateTime validTime;

    /**
     * 报价单的失效时间
     */
    @NotNull(groups = {Add.class, Update.class} , message = "invalidTime is not null")
    private LocalDateTime  invalidTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 商城是否展示 0 展示 1 不展示 类似上下架
     */
    @NotNull(groups = {Add.class, Update.class} , message = "mallShow is not null")
    private Integer mallShow;

    /**
     * 价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）3商城价上浮 4商城价下浮 5商城价加价 6商城价减价
     */
    @NotNull(groups = {Add.class, Update.class} , message = "priceType is not null")
    private Integer priceType;
    /**
     * 毛利率
     */
    private BigDecimal interestRate;
    /**
     * 指定价
     */
    private BigDecimal price;
    /**
     * 毛利率的固定价
     */
    private BigDecimal fixedPrice;
    /**
     * 毛利率的成本价
     */
    private BigDecimal cost;
    /**
     * 商城价浮动/加减 值
     */
    private BigDecimal priceAdjustmentValue;
}
