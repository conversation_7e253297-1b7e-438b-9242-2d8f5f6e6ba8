package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import lombok.Data;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactVO;
import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
/**
 * 更换账号待审核
 */
public class AccountChangeVO extends MerchantVO implements Serializable {
    private static final long serialVersionUID = 5640865689281001205L;
    /**
     * 门店账号修改的记录
     */
    private AccountChangeEntity changeRecord;

}
