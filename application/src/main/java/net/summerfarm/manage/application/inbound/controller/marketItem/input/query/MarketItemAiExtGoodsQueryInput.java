package net.summerfarm.manage.application.inbound.controller.marketItem.input.query;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class MarketItemAiExtGoodsQueryInput implements Serializable {

    /**
     * 商品编码
     */
    @NotEmpty(message = "商品编码不能为空")
    @Size(max = 10, message = "商品编码长度不能超过10")
    private List<String> skus;

    /**
     * 秘钥
     */
    private String ssKey;
}
