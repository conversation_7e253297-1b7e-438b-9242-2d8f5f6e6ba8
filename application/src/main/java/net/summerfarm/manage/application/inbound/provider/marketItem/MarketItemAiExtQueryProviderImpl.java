package net.summerfarm.manage.application.inbound.provider.marketItem;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.common.enums.MarketItemAiExtTypeEnum;
import net.summerfarm.client.provider.marketItem.MarketItemAiExtQueryProvider;
import net.summerfarm.client.req.marketItem.MarketItemAiExtQueryReq;
import net.summerfarm.client.resp.marketItem.MarketItemRelationQuestionResp;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-03 16:33:54
 */
@DubboService
@Slf4j
public class MarketItemAiExtQueryProviderImpl implements MarketItemAiExtQueryProvider {

    @Autowired
    private MarketItemAiExtQueryRepository marketItemAiExtQueryRepository;

    @Override
    public DubboResponse<MarketItemRelationQuestionResp> getMarketItemRelationQuestion(@Valid MarketItemAiExtQueryReq marketItemAiExtQueryReq) {
        if (!MarketItemAiExtTypeEnum.isExist(marketItemAiExtQueryReq.getExtType())) {
            return DubboResponse.getDefaultError("请求参数错误，请求extType不支持");
        }
        MarketItemRelationQuestionResp resp = new MarketItemRelationQuestionResp();
        resp.setQuestionList(new ArrayList<>());

        if (!Integer.valueOf(MarketItemAiExtTypeEnum.RELATION_QUESTION.getCode())
                .equals(marketItemAiExtQueryReq.getExtType())) {
            return DubboResponse.getOK(resp);
        }

        List<MarketItemAiExtEntity> entities = marketItemAiExtQueryRepository
                .selectBySkuAndExtType(marketItemAiExtQueryReq.getSku(), marketItemAiExtQueryReq.getExtType());
        if (entities.isEmpty()) {
            return DubboResponse.getOK(resp);
        }

        // MarketItemAiExtEntity::getExtValue json数组转list
        // 遍历list，取出每个json对象的question字段，放入resp的questionList中
        // 取出json对象的question字段，放入resp的questionList中
        try {
            for (MarketItemAiExtEntity entity : entities) {
                resp.getQuestionList().addAll(
                        JSONObject.parseArray(entity.getExtValue(), String.class));
            }
        } catch (Exception e) {
            log.error("MarketItemAiExtQueryProviderImpl.getMarketItemRelationQuestion error", e);
            return DubboResponse.getOK(resp);
        }

        return DubboResponse.getOK(resp);
    }

}
