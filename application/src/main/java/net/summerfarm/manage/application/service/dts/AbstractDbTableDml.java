package net.summerfarm.manage.application.service.dts;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.converter.BinLogConverter;
import net.summerfarm.manage.common.converter.FastJsonConverter;
import net.summerfarm.manage.common.dto.DtsModel;
import net.summerfarm.manage.common.dto.XmPair;
import net.xianmu.common.exception.BizException;



import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/4/28 11:32
 */

@Slf4j
public abstract class AbstractDbTableDml implements DbTableDml {


    @Override
    public void handle(DtsModel dtsModel) {
        List<XmPair<Map<String, String>, Map<String, String>>> pairList = BinLogConverter.getAlignedData(dtsModel);
        if (CollUtil.isEmpty(pairList)) {
            log.warn("DtsModel数据格式有误!!");
            return;
        }

        // 数据转换
        for (XmPair<Map<String, String>, Map<String, String>> pair : pairList) {
            try {
                log.info("当前处理数据：data: {}", FastJsonConverter.convert(pair));
                this.doService(pair, dtsModel);
            }  catch (BizException e) {
                log.warn("binlog处理失败! biz exception code :{}, message:{}", FastJsonConverter.convert(e.getErrorCode()), e.getMessage(), e);
            } catch (Exception e) {
                log.error("fail pair data :{}", FastJsonConverter.convert(pair));
                // 直接告警，人工补偿
                log.error("binlog处理失败!", e);
            }
        }

    }


    /**
     * 解析binlog，触发业务同步
     *
     * @param pair
     * @param dtsModel
     */
    public abstract void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel);





}
