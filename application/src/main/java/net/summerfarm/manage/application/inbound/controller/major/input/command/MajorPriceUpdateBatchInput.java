package net.summerfarm.manage.application.inbound.controller.major.input.command;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
@Data
public class MajorPriceUpdateBatchInput {
    private List<Long> ids;
    /**
     * 报价单的生效时间
     */
    @NotNull(groups = {Add.class, Update.class} , message = "validTime is not null")
    private LocalDateTime validTime;

    /**
     * 报价单的失效时间
     */
    @NotNull(groups = {Add.class, Update.class} , message = "invalidTime is not null")
    private LocalDateTime  invalidTime;

    @NotNull(groups = {Add.class, Update.class} , message = "priceType is not null")
    private Integer priceType;
    /**
     * 毛利率
     */
    private BigDecimal interestRate;
    /**
     * 毛利率的固定价
     */
    private BigDecimal fixedPrice;
    /**
     * 毛利率的成本价
     */
    private BigDecimal cost;
    /**
     * 商城价浮动/加减 值
     */
    private BigDecimal priceAdjustmentValue;
    /**
     * 指定价
     */
    private BigDecimal price;
}
