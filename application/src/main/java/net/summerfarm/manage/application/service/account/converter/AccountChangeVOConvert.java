package net.summerfarm.manage.application.service.account.converter;

import net.summerfarm.manage.application.inbound.controller.merchant.vo.AccountChangeVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class AccountChangeVOConvert {


    private AccountChangeVOConvert() {
        // 无需实现
    }


    public static void warpAccountChangeVOList(List<AccountChangeVO> vos, List<MerchantVO> merchantVOList) {
        if (vos == null || merchantVOList == null) {
            return;
        }
        Map<Long, MerchantVO> collect = merchantVOList.stream().collect(Collectors.toMap(MerchantVO::getMId, Function.identity()));
        Iterator<AccountChangeVO> iterator = vos.iterator();
        while (iterator.hasNext()) {
            AccountChangeVO vo = iterator.next();
            if (collect.get(vo.getMId()) == null) {
                iterator.remove();
            } else {
                warpAccountChangeVO(vo, collect.get(vo.getMId()));
            }
        }
    }

    public static void warpAccountChangeVO(AccountChangeVO vo, MerchantVO merchantVO) {
        if (merchantVO == null || vo == null) {
            return;
        }
        vo.setMId(merchantVO.getMId());
        vo.setStoreId(merchantVO.getStoreId());
        vo.setRoleId(merchantVO.getRoleId());
        vo.setMname(merchantVO.getMname());
        vo.setMcontact(merchantVO.getMcontact());
        vo.setOpenid(merchantVO.getOpenid());
        vo.setPhone(merchantVO.getPhone());
        vo.setStatus(merchantVO.getStatus());
        vo.setIslock(merchantVO.getIslock());
        vo.setRankId(merchantVO.getRankId());
        vo.setRegisterTime(merchantVO.getRegisterTime());
        vo.setLoginTime(merchantVO.getLoginTime());
        vo.setInvitecode(merchantVO.getInvitecode());
        vo.setChannelCode(merchantVO.getChannelCode());
        vo.setInviterChannelCode(merchantVO.getInviterChannelCode());
        vo.setAuditUser(merchantVO.getAuditUser());
        vo.setBusinessLicense(merchantVO.getBusinessLicense());
        vo.setProvince(merchantVO.getProvince());
        vo.setCity(merchantVO.getCity());
        vo.setArea(merchantVO.getArea());
        vo.setAddress(merchantVO.getAddress());
        vo.setPoiNote(merchantVO.getPoiNote());
        vo.setRemark(merchantVO.getRemark());
        vo.setShopSign(merchantVO.getShopSign());
        vo.setOtherProof(merchantVO.getOtherProof());
        vo.setLastOrderTime(merchantVO.getLastOrderTime());
        vo.setAreaNo(merchantVO.getAreaNo());
        vo.setSize(merchantVO.getSize());
        vo.setType(merchantVO.getType());
        vo.setTradeArea(merchantVO.getTradeArea());
        vo.setTradeGroup(merchantVO.getTradeGroup());
        vo.setUnionid(merchantVO.getUnionid());
        vo.setMpOpenid(merchantVO.getMpOpenid());
        vo.setAdminId(merchantVO.getAdminId());
        vo.setDirect(merchantVO.getDirect());
        vo.setServer(merchantVO.getServer());
        vo.setPopView(merchantVO.getPopView());
        vo.setMemberIntegral(merchantVO.getMemberIntegral());
        vo.setGrade(merchantVO.getGrade());
        vo.setSkuShow(merchantVO.getSkuShow());
        vo.setRechargeAmount(merchantVO.getRechargeAmount());
        vo.setCashAmount(merchantVO.getCashAmount());
        vo.setCashUpdateTime(merchantVO.getCashUpdateTime());
        vo.setShowPrice(merchantVO.getShowPrice());
        vo.setMergeAdmin(merchantVO.getMergeAdmin());
        vo.setMergeTime(merchantVO.getMergeTime());
        vo.setFirstLoginPop(merchantVO.getFirstLoginPop());
        vo.setChangePop(merchantVO.getChangePop());
        vo.setPullBlackRemark(merchantVO.getPullBlackRemark());
        vo.setPullBlackOperator(merchantVO.getPullBlackOperator());
        vo.setHouseNumber(merchantVO.getHouseNumber());
        vo.setCompanyBrand(merchantVO.getCompanyBrand());
        vo.setCluePool(merchantVO.getCluePool());
        vo.setMerchantType(merchantVO.getMerchantType());
        vo.setEnterpriseScale(merchantVO.getEnterpriseScale());
        vo.setUpdateTime(merchantVO.getUpdateTime());
        vo.setExamineType(merchantVO.getExamineType());
        vo.setDisplayButton(merchantVO.getDisplayButton());
        vo.setOperateStatus(merchantVO.getOperateStatus());
        vo.setUpdater(merchantVO.getUpdater());
        vo.setDoorPic(merchantVO.getDoorPic());
        vo.setPreRegisterFlag(merchantVO.getPreRegisterFlag());
        vo.setAdminRealName(merchantVO.getAdminRealName());
        vo.setAreaName(merchantVO.getAreaName());
        vo.setInvoiceTitle(merchantVO.getInvoiceTitle());
        vo.setContacts(merchantVO.getContacts());
// Not mapped TO fields:
// auditTime
// changeRecord
// auditTime
// Not mapped FROM fields:
// auditTime
    }


    public static List<AccountChangeVO> toAccountChangeVOList(List<AccountChangeEntity> entityList) {
        if (entityList == null) {
            return Collections.emptyList();
        }
        List<AccountChangeVO> voList = new ArrayList<>();
        for (AccountChangeEntity entity : entityList) {
            voList.add(toAccountChangeVO(entity));
        }
        return voList;
    }

    public static AccountChangeVO toAccountChangeVO(AccountChangeEntity entity) {
        if (entity == null) {
            return null;
        }
        AccountChangeVO vo = new AccountChangeVO();
        vo.setMId(entity.getMId());
        vo.setChangeRecord(entity);
        return vo;
    }


}
