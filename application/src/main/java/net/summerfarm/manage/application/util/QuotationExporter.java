package net.summerfarm.manage.application.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import net.summerfarm.manage.common.constants.AppConsts;
import net.summerfarm.manage.common.enums.download.ExcelTypeEnum;
import net.summerfarm.manage.domain.major.dto.QuotationExcelDto;
import net.summerfarm.manage.domain.major.dto.QuotationExcelHeadDto;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/2/24 13:48
 */
public class QuotationExporter {


    /**
     * 导出报价单
     *
     * @param
     * @param dataSupplier 商品数据提供接口
     * @param headerTitle  标题（示例："XX品牌-报价函"）
     *  validPeriod  有效期（示例："2024.4.12-2024.4.20"）
     * @param quoter       报价人
     * @param quoteDate    报价日期
     */
    public static String export(String fileName,
                                Supplier<List<QuotationExcelDto>> dataSupplier,
                                String headerTitle,
                                String quoter,
                                String quoteDate) throws Exception {

        ExcelWriter excelWriter = null;
        File tempFile = null;
        try {
            // 构建导出
            List<QuotationExcelDto> data = dataSupplier.get();
            // 这里导出的数据可能不是同一批报价的sku，应产品需求：默认取列表第一条数据的有效期
            String validPeriod = CollectionUtil.isNotEmpty(data) ? data.get(0).getValidPeriod() : "";
            QuotationExcelHeadDto headDto = new QuotationExcelHeadDto(headerTitle, quoter, quoteDate, validPeriod);
            // 自动填充序号（可选）
            for (int i = 0; i < data.size(); i++) {
                data.get(i).setSeqNo(i + 1);
            }

            // 模板文件

            InputStream templateFileInputStream = QuotationExporter.class.getClassLoader()
                    .getResourceAsStream(AppConsts.EXCEL_DIRECTORY + AppConsts.SLASH + ExcelTypeEnum.MAJOR_PRICE_EXCEL.getName());

            // 创建临时文件
            String tempFilePath = System.getProperty("user.dir") + File.separator + System.currentTimeMillis() + com.alibaba.excel.support.ExcelTypeEnum.XLSX.getValue();
            tempFile = new File(tempFilePath);


            excelWriter = EasyExcel.write(tempFile).withTemplate(templateFileInputStream).build();
            WriteSheet sheet = EasyExcel.writerSheet("报价单")
                    .build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).direction(WriteDirectionEnum.VERTICAL).build();

            excelWriter.fill(data, fillConfig, sheet);
            excelWriter.fill(headDto, fillConfig, sheet);
            excelWriter.finish();

            OssUploadResult ossUploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(tempFile), OSSExpiredLabelEnum.MONTH);
            return ossUploadResult.getObjectOssKey();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                Files.deleteIfExists(tempFile.toPath());
            }
        }
    }
}
