package net.summerfarm.manage.application.inbound.controller.product;

import net.summerfarm.manage.application.inbound.controller.product.input.ProductInfoInput;
import net.summerfarm.manage.application.inbound.controller.product.input.command.WarnTimeCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductVO;
import net.summerfarm.manage.application.service.product.mall.ProductCommandService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description
 * @Date 2025/4/17 14:51
 * @<AUTHOR>
 */
@RestController
@RequestMapping("/product-tool")
public class ProductToolController {

    @Resource
    private ProductCommandService productCommandService;


    @RequestMapping(value = "/update/warnTime", method = RequestMethod.POST)
    public CommonResult<ProductVO> batchUpdateWarnTime(@RequestBody @Valid List<WarnTimeCommandInput> inputList) {
        productCommandService.batchUpdateWarnTime(inputList);
        return CommonResult.ok();
    }



}
