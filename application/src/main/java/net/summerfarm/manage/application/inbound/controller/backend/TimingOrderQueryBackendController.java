package net.summerfarm.manage.application.inbound.controller.backend;

import net.summerfarm.manage.application.inbound.controller.backend.input.TimingOrderNoFreezeProxySaleQueryInput;
import net.summerfarm.manage.application.service.order.OrderQueryService;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: 省心送查询后门接口<br/>
 * date: 2024/6/5 17:47<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/backend")
public class TimingOrderQueryBackendController {

    @Resource
    private OrderQueryService orderQueryService;

    /**
     * 查询省心送代销不如仓未冻结数据
     * @param input 入参
     * @return 结果
     */
    @PostMapping(value = "/query/queryTimingOrderNoFreezeProxySaleNoWarehouse")
    public CommonResult<List<NoFreezeProxySaleNoWareNoSkuFlatObject>> queryTimingOrderNoFreezeProxySaleNoWarehouse(@RequestBody TimingOrderNoFreezeProxySaleQueryInput input) {
        List<NoFreezeProxySaleNoWareNoSkuFlatObject> noFreezeProxySaleNoWareNoSkuFlatObjects = orderQueryService.queryTimingOrderNoFreezeProxySaleNoWarehouse(input.getStartDate(), input.getEndDate(), input.getStoreNo());
        return CommonResult.ok(noFreezeProxySaleNoWareNoSkuFlatObjects);
    }


    /**
     * 查询未设置省心送代销不如仓订单数据
     * @param input 入参
     * @return 结果
     */
    @PostMapping(value = "/query/queryTimingOrderNoSetOrderProxySaleNoWarehouseOrderInfo")
    public CommonResult<List<TimingOrderProxySaleNoWarehouseSkuFlatObject>> queryTimingOrderNoSetOrderProxySaleNoWarehouseOrderInfo(@RequestBody TimingOrderNoFreezeProxySaleQueryInput input) {
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> noFreezeProxySaleNoWareNoSkuFlatObjects = orderQueryService.queryTimingOrderNoSetOrderProxySaleNoWarehouseOrderInfo(input.getStoreNo());
        return CommonResult.ok(noFreezeProxySaleNoWareNoSkuFlatObjects);
    }
}
