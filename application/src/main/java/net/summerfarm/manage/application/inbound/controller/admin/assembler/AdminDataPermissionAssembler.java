package net.summerfarm.manage.application.inbound.controller.admin.assembler;


import java.util.ArrayList;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminDataPermissionVO;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.application.inbound.controller.admin.input.command.AdminDataPermissionCommandInput;
import net.summerfarm.manage.application.inbound.controller.admin.input.query.AdminDataPermissionQueryInput;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;
import net.summerfarm.manage.domain.admin.param.command.AdminDataPermissionCommandParam;
import java.util.List;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-06-19 16:33:43
 * @version 1.0
 *
 */
public class AdminDataPermissionAssembler {

    private AdminDataPermissionAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static AdminDataPermissionQueryParam toAdminDataPermissionQueryParam(AdminDataPermissionQueryInput adminDataPermissionQueryInput) {
        if (adminDataPermissionQueryInput == null) {
            return null;
        }
        AdminDataPermissionQueryParam adminDataPermissionQueryParam = new AdminDataPermissionQueryParam();
        adminDataPermissionQueryParam.setId(adminDataPermissionQueryInput.getId());
        adminDataPermissionQueryParam.setAdminId(adminDataPermissionQueryInput.getAdminId());
        adminDataPermissionQueryParam.setPermissionValue(adminDataPermissionQueryInput.getPermissionValue());
        adminDataPermissionQueryParam.setPermissionName(adminDataPermissionQueryInput.getPermissionName());
        adminDataPermissionQueryParam.setAddtime(adminDataPermissionQueryInput.getAddtime());
        adminDataPermissionQueryParam.setType(adminDataPermissionQueryInput.getType());
        adminDataPermissionQueryParam.setPageIndex(adminDataPermissionQueryInput.getPageIndex());
        adminDataPermissionQueryParam.setPageSize(adminDataPermissionQueryInput.getPageSize());
        return adminDataPermissionQueryParam;
    }





    public static AdminDataPermissionCommandParam buildCreateParam(AdminDataPermissionCommandInput adminDataPermissionCommandInput) {
        if (adminDataPermissionCommandInput == null) {
            return null;
        }
        AdminDataPermissionCommandParam adminDataPermissionCommandParam = new AdminDataPermissionCommandParam();
        adminDataPermissionCommandParam.setId(adminDataPermissionCommandInput.getId());
        adminDataPermissionCommandParam.setAdminId(adminDataPermissionCommandInput.getAdminId());
        adminDataPermissionCommandParam.setPermissionValue(adminDataPermissionCommandInput.getPermissionValue());
        adminDataPermissionCommandParam.setPermissionName(adminDataPermissionCommandInput.getPermissionName());
        adminDataPermissionCommandParam.setAddtime(adminDataPermissionCommandInput.getAddtime());
        adminDataPermissionCommandParam.setType(adminDataPermissionCommandInput.getType());
        return adminDataPermissionCommandParam;
    }


    public static AdminDataPermissionCommandParam buildUpdateParam(AdminDataPermissionCommandInput adminDataPermissionCommandInput) {
        if (adminDataPermissionCommandInput == null) {
            return null;
        }
        AdminDataPermissionCommandParam adminDataPermissionCommandParam = new AdminDataPermissionCommandParam();
        adminDataPermissionCommandParam.setId(adminDataPermissionCommandInput.getId());
        adminDataPermissionCommandParam.setAdminId(adminDataPermissionCommandInput.getAdminId());
        adminDataPermissionCommandParam.setPermissionValue(adminDataPermissionCommandInput.getPermissionValue());
        adminDataPermissionCommandParam.setPermissionName(adminDataPermissionCommandInput.getPermissionName());
        adminDataPermissionCommandParam.setAddtime(adminDataPermissionCommandInput.getAddtime());
        adminDataPermissionCommandParam.setType(adminDataPermissionCommandInput.getType());
        return adminDataPermissionCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<AdminDataPermissionVO> toAdminDataPermissionVOList(List<AdminDataPermissionEntity> adminDataPermissionEntityList) {
        if (adminDataPermissionEntityList == null) {
            return Collections.emptyList();
        }
        List<AdminDataPermissionVO> adminDataPermissionVOList = new ArrayList<>();
        for (AdminDataPermissionEntity adminDataPermissionEntity : adminDataPermissionEntityList) {
            adminDataPermissionVOList.add(toAdminDataPermissionVO(adminDataPermissionEntity));
        }
        return adminDataPermissionVOList;
}


   public static AdminDataPermissionVO toAdminDataPermissionVO(AdminDataPermissionEntity adminDataPermissionEntity) {
       if (adminDataPermissionEntity == null) {
            return null;
       }
       AdminDataPermissionVO adminDataPermissionVO = new AdminDataPermissionVO();
       adminDataPermissionVO.setAdminId(adminDataPermissionEntity.getAdminId());
       adminDataPermissionVO.setPermissionValue(adminDataPermissionEntity.getPermissionValue());
       adminDataPermissionVO.setPermissionName(adminDataPermissionEntity.getPermissionName());
       adminDataPermissionVO.setType(adminDataPermissionEntity.getType());
       return adminDataPermissionVO;
   }

}
