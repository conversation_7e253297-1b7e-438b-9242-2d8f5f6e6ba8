package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.apache.poi.ss.usermodel.DateUtil;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销
 * @date 2023/4/20 16:35:04
 */
@Data
public class MerchantCancelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String merchantName;

    /**
     * BD实际名称
     */
    private String adminRealName;

    /**
     * 主联系人
     */
    private String mcontact;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 运营区域
     */
    private String areaNo;

    /**
     * 运营区域
     */
    private String areaName;

    /**
     * 注册省
     */
    private String province;

    /**
     * 注册市
     */
    private String city;

    /**
     * 注册区
     */
    private String area;

    /**
     * 注册地址
     */
    private String address;

    /**
     * 商家腾讯地图坐标
     */
    private String poiNote;

    /**
     * 门店类型
     */
    private String size;

    /**
     * 注销状态 默认-1（待注销） MerchantCancelEnum
     * @see MerchantCancelEnum
     */
    private Integer status;

    /**
     * 审核状态
     * @see MerchantIsLockEnum
     */
    private Integer isLock;

    /**
     * 申请原因
     */
    private String remake;

    /**
     * 注销来源 默认商城-0
     * @see CommonStatus
     */
    private Integer resource;

    /**
     * 申请凭证-后台申请必填
     */
    private String certificate;

    /**
     * 校验错误统一提示给
     */
    private List<String> cause;

    /**
     * 申请人
     */
    private OperatorVO creator;

    /**
     * 更新人
     */
    private OperatorVO updater;

    /**
     * 申请时间
     */
    private LocalDateTime createTime;

    /**
     * 注销时间
     */
    private LocalDateTime updateTime;



    public static void wrapVoByMerchant(List<MerchantStoreAndExtendResp> sourceList, List<MerchantCancelVO> targetList){
        if(CollUtil.isEmpty(sourceList) || CollUtil.isEmpty(targetList)) {
            return;
        }
        Map<Long, MerchantStoreAndExtendResp> collect = sourceList.stream().collect(Collectors.toMap(MerchantStoreAndExtendResp::getMId, Function.identity()));
        MerchantStoreAndExtendResp temp;
        for (MerchantCancelVO entity : targetList) {
            // 鲜沐数据
            temp = collect.get(entity.getMId());
            if(null != temp) {
                entity.setMcontact(temp.getAccountName());
                entity.setProvince(temp.getProvince());
                entity.setCity(temp.getCity());
                entity.setArea(temp.getArea());
                entity.setPoiNote(temp.getPoiNote());
                entity.setSize(MerchantEntity.transSizeFromUserCenter(temp.getSize()));
                entity.setIsLock(MerchantEntity.transStatusFromUserCenter(temp.getSize()));
            }
        }
    }


    public static void wrapVoByAccount(List<MerchantStoreAccountResultResp> sourceList, List<MerchantCancelVO> targetList){
        if(CollUtil.isEmpty(sourceList) || CollUtil.isEmpty(targetList)) {
            return;
        }
        Map<Long, List<MerchantStoreAccountResultResp>> accountMap = sourceList.stream().collect(Collectors.groupingBy(MerchantStoreAccountResultResp::getMId));
        for (MerchantCancelVO vo : targetList) {
            List<MerchantStoreAccountResultResp> list = accountMap.get(vo.getMId());
            if(CollUtil.isNotEmpty(list)) {
                list.sort(Comparator.comparing(MerchantStoreAccountResultResp::getDeleteFlag, Comparator.reverseOrder()).thenComparing(MerchantStoreAccountResultResp::getStoreId, Comparator.reverseOrder()));
                vo.setMcontact(list.get(0).getAccountName());
            }
        }
    }
}
