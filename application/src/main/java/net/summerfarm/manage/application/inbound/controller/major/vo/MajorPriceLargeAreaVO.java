package net.summerfarm.manage.application.inbound.controller.major.vo;

import lombok.Data;

import java.util.List;

@Data
public class MajorPriceLargeAreaVO {

    /**
     * 运营大区
     */
    private Integer largeAreaNo;

    /**
     * 运营大区name
     */
    private String largeAreaName;

    /**
     * sku编号
     */
    private String sku;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 城市报价
     */
    private List<MajorPriceVO> majorPrices;
}
