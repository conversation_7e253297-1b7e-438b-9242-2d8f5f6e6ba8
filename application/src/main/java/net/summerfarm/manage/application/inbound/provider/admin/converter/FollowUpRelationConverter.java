package net.summerfarm.manage.application.inbound.provider.admin.converter;

import net.summerfarm.client.resp.admin.FollowUpRelationQueryResp;
import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/19 16:18
 * @PackageName:net.summerfarm.manage.application.inbound.provider.admin.converter
 * @ClassName: FollowUpRelationConverter
 * @Description: TODO
 * @Version 1.0
 */
public class FollowUpRelationConverter {
    public static List<FollowUpRelationQueryResp> toFollowUpRelationQueryRespList(List<FollowUpRelationEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        List<FollowUpRelationQueryResp> followUpRelationQueryResps = new ArrayList<>();
        entities.forEach(entity -> {
            FollowUpRelationQueryResp followUpRelationQueryResp = new FollowUpRelationQueryResp();
            followUpRelationQueryResp.setMId(entity.getMId());
            followUpRelationQueryResp.setFollowType(entity.getFollowType());
            followUpRelationQueryResp.setAddTime(entity.getAddTime());
            followUpRelationQueryResp.setLastFollowUpTime(entity.getLastFollowUpTime());
            followUpRelationQueryResp.setReason(entity.getReason());
            followUpRelationQueryResp.setAdminName(entity.getAdminName());
            followUpRelationQueryResp.setAdminId(entity.getAdminId());
            followUpRelationQueryResp.setCareBdId(entity.getCareBdId());
            followUpRelationQueryResp.setDangerDay(entity.getDangerDay());
            followUpRelationQueryResp.setTimingFollowType(entity.getTimingFollowType());
            followUpRelationQueryResp.setReassignTime(entity.getReassignTime());
            followUpRelationQueryResp.setId(entity.getId());
            followUpRelationQueryResp.setReassign(entity.getReassign());
            followUpRelationQueryResps.add(followUpRelationQueryResp);
        });
        return followUpRelationQueryResps;
    }
}
