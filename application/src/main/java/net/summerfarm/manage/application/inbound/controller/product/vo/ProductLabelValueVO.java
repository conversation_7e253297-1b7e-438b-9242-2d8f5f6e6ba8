package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ProductLabelValue
 * @Description
 * <AUTHOR>
 * @Date 18:41 2024/5/8
 * @Version 1.0
 **/
@Data
public class ProductLabelValueVO implements Serializable {

    /**
     * SKU标签表
     */
    private String labelField;

    /**
     * SKU标签表
     */
    private String labelName;

    /**
     * id
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 标签字段名
     */
    private String labelId;

    /**
     * 标签值
     */
    private Integer labelValue;
}
