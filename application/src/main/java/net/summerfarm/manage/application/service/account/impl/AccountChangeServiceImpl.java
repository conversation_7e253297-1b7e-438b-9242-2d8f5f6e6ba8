package net.summerfarm.manage.application.service.account.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.AccountChangeVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactAdjustVO;
import net.summerfarm.manage.application.service.account.AccountChangeService;
import net.summerfarm.manage.application.service.account.converter.AccountChangeVOConvert;
import net.summerfarm.manage.application.service.contact.converter.ContactAdjustVOConverter;
import net.summerfarm.manage.application.service.merchant.MerchantBaseService;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.common.util.PageUtils;
import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;
import net.summerfarm.manage.domain.account.repository.AccountChangeQueryRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AccountChangeServiceImpl extends MerchantBaseService implements AccountChangeService {
    @Resource
    private AccountChangeQueryRepository accountChangeQueryRepository;

    /**
     * @param selectKeys 查询key
     * @return
     */
    @Override
    public PageInfo<AccountChangeVO> queryPage(MerchantQueryInput selectKeys) {
        PageInfo<AccountChangeVO> pageInfo = new PageInfo<>();
        List<AccountChangeVO> pageSizeDataForRelations = PageUtils.getPageSizeDataForRelations(list(selectKeys), selectKeys.getPageSize(), selectKeys.getPageIndex());
        pageInfo.setTotal(pageSizeDataForRelations.size());
        pageInfo.setPageNum(selectKeys.getPageIndex());
        pageInfo.setPageSize(selectKeys.getPageSize());
        pageInfo.setList(pageSizeDataForRelations);
        return pageInfo;
    }


    @Override
    public AccountChangeVO changeDetail(MerchantQueryInput selectKeys) {
        List<AccountChangeVO> list = list(selectKeys);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }


    @Override
    public List<AccountChangeEntity> getQueryInput(MerchantQueryInput input) {
        return accountChangeQueryRepository.getQueryInput(input);
    }

    public List<AccountChangeVO> list(MerchantQueryInput input) {
        input.setAccountChangeStatus(1);
        PageHelper.startPage(PageUtils.DEFAULT_PAGE_NO, PageUtils.DEFAULT_PAGE_SIZE);
        List<AccountChangeEntity> changeList = getQueryInput(input);
        if (CollectionUtils.isEmpty(changeList)) {
            return new ArrayList<>();
        }

        List<Long> mIds = changeList.stream().map(AccountChangeEntity::getMId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mIds)) {
            return new ArrayList<>();
        }

        List<AccountChangeVO> vos = AccountChangeVOConvert.toAccountChangeVOList(changeList);

        // 拼接列表数据
        input.setMIds(mIds);
        input.setPageIndex(PageUtils.DEFAULT_PAGE_NO);
        input.setPageSize(mIds.size());
        //根据状态查询
        PageInfo<MerchantVO> page = getBasePage(input);
        List<MerchantVO> merchantStoreAndExtends = page.getList();
        //合并数据
        baseListMerge(merchantStoreAndExtends);
        AccountChangeVOConvert.warpAccountChangeVOList(vos, merchantStoreAndExtends);
        return vos;
    }
}
