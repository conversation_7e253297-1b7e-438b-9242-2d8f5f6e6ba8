package net.summerfarm.manage.application.inbound.controller.merchant.assembler;


import cn.hutool.core.util.StrUtil;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCommandInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MerchantAssembler {


    private MerchantAssembler() {
        // 无需实现
    }






    public static MerchantEntity toMerchantEntity(MerchantCommandInput merchantCommandInput) {
        if (merchantCommandInput == null) {
            return null;
        }
        MerchantEntity merchantEntity = new MerchantEntity();
        merchantEntity.setMId(merchantCommandInput.getMId());
        merchantEntity.setRoleId(merchantCommandInput.getRoleId());
        merchantEntity.setMname(merchantCommandInput.getMname());
        merchantEntity.setMcontact(merchantCommandInput.getMcontact());
        merchantEntity.setOpenid(merchantCommandInput.getOpenid());
        merchantEntity.setPhone(merchantCommandInput.getPhone());
        merchantEntity.setStatus(merchantCommandInput.getStatus());
        merchantEntity.setIslock(merchantCommandInput.getIslock());
        merchantEntity.setRankId(merchantCommandInput.getRankId());
        merchantEntity.setRegisterTime(merchantCommandInput.getRegisterTime());
        merchantEntity.setLoginTime(merchantCommandInput.getLoginTime());
        merchantEntity.setInvitecode(merchantCommandInput.getInvitecode());
        merchantEntity.setChannelCode(merchantCommandInput.getChannelCode());
        merchantEntity.setInviterChannelCode(merchantCommandInput.getInviterChannelCode());
        merchantEntity.setAuditTime(merchantCommandInput.getAuditTime());
        merchantEntity.setAuditUser(merchantCommandInput.getAuditUser());
        merchantEntity.setBusinessLicense(merchantCommandInput.getBusinessLicense());
        merchantEntity.setProvince(merchantCommandInput.getProvince());
        merchantEntity.setCity(merchantCommandInput.getCity());
        merchantEntity.setArea(merchantCommandInput.getArea());
        merchantEntity.setAddress(merchantCommandInput.getAddress());
        merchantEntity.setPoiNote(merchantCommandInput.getPoiNote());
        merchantEntity.setRemark(merchantCommandInput.getRemark());
        merchantEntity.setShopSign(merchantCommandInput.getShopSign());
        merchantEntity.setOtherProof(merchantCommandInput.getOtherProof());
        merchantEntity.setLastOrderTime(merchantCommandInput.getLastOrderTime());
        merchantEntity.setAreaNo(merchantCommandInput.getAreaNo());
        merchantEntity.setSize(merchantCommandInput.getSize());
        merchantEntity.setType(merchantCommandInput.getType());
        merchantEntity.setTradeArea(merchantCommandInput.getTradeArea());
        merchantEntity.setTradeGroup(merchantCommandInput.getTradeGroup());
        merchantEntity.setUnionid(merchantCommandInput.getUnionid());
        merchantEntity.setMpOpenid(merchantCommandInput.getMpOpenid());
        merchantEntity.setAdminId(merchantCommandInput.getAdminId());
        merchantEntity.setDirect(merchantCommandInput.getDirect());
        merchantEntity.setServer(merchantCommandInput.getServer());
        merchantEntity.setPopView(merchantCommandInput.getPopView());
        merchantEntity.setMemberIntegral(merchantCommandInput.getMemberIntegral());
        merchantEntity.setGrade(merchantCommandInput.getGrade());
        merchantEntity.setSkuShow(merchantCommandInput.getSkuShow());
        merchantEntity.setRechargeAmount(merchantCommandInput.getRechargeAmount());
        merchantEntity.setCashAmount(merchantCommandInput.getCashAmount());
        merchantEntity.setCashUpdateTime(merchantCommandInput.getCashUpdateTime());
        merchantEntity.setShowPrice(merchantCommandInput.getShowPrice());
        merchantEntity.setMergeAdmin(merchantCommandInput.getMergeAdmin());
        merchantEntity.setMergeTime(merchantCommandInput.getMergeTime());
        merchantEntity.setFirstLoginPop(merchantCommandInput.getFirstLoginPop());
        merchantEntity.setChangePop(merchantCommandInput.getChangePop());
        merchantEntity.setPullBlackRemark(merchantCommandInput.getPullBlackRemark());
        merchantEntity.setPullBlackOperator(merchantCommandInput.getPullBlackOperator());
        merchantEntity.setHouseNumber(merchantCommandInput.getHouseNumber());
        merchantEntity.setCompanyBrand(merchantCommandInput.getCompanyBrand());
        merchantEntity.setCluePool(merchantCommandInput.getCluePool());
        merchantEntity.setMerchantType(merchantCommandInput.getMerchantType());
        merchantEntity.setEnterpriseScale(merchantCommandInput.getEnterpriseScale());
        merchantEntity.setUpdateTime(merchantCommandInput.getUpdateTime());
        merchantEntity.setExamineType(merchantCommandInput.getExamineType());
        merchantEntity.setDisplayButton(merchantCommandInput.getDisplayButton());
        merchantEntity.setOperateStatus(merchantCommandInput.getOperateStatus());
        merchantEntity.setUpdater(merchantCommandInput.getUpdater());
        merchantEntity.setDoorPic(merchantCommandInput.getDoorPic());
        merchantEntity.setPreRegisterFlag(merchantCommandInput.getPreRegisterFlag());
        merchantEntity.setPrintOutTMSConfig(merchantCommandInput.getPrintOutTMSConfig());
        return merchantEntity;
    }


    public static List<MerchantVO> toMerchantVOList(List<MerchantEntity> merchantEntityList) {
        if (merchantEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantVO> merchantVOList = new ArrayList<>();
        for (MerchantEntity merchantEntity : merchantEntityList) {
            merchantVOList.add(toMerchantVO(merchantEntity));
        }
        return merchantVOList;
    }

    public static MerchantVO toMerchantVO(MerchantEntity merchantEntity) {
        if (merchantEntity == null) {
            return null;
        }
        MerchantVO merchantVO = new MerchantVO();
        merchantVO.setMId(merchantEntity.getMId());
        merchantVO.setRoleId(merchantEntity.getRoleId());
        merchantVO.setMname(merchantEntity.getMname());
        merchantVO.setMcontact(merchantEntity.getMcontact());
        merchantVO.setOpenid(merchantEntity.getOpenid());
        merchantVO.setPhone(merchantEntity.getPhone());
        merchantVO.setStatus(merchantEntity.getStatus());
        merchantVO.setRankId(merchantEntity.getRankId());
        merchantVO.setRegisterTime(merchantEntity.getRegisterTime());
        merchantVO.setLoginTime(merchantEntity.getLoginTime());
        merchantVO.setInvitecode(merchantEntity.getInvitecode());
        merchantVO.setChannelCode(merchantEntity.getChannelCode());
        merchantVO.setInviterChannelCode(merchantEntity.getInviterChannelCode());
        merchantVO.setAuditTime(merchantEntity.getAuditTime());
        merchantVO.setAuditUser(merchantEntity.getAuditUser());
        merchantVO.setBusinessLicense(merchantEntity.getBusinessLicense());
        merchantVO.setProvince(merchantEntity.getProvince());
        merchantVO.setCity(merchantEntity.getCity());
        merchantVO.setArea(merchantEntity.getArea());
        merchantVO.setAddress(merchantEntity.getAddress());
        merchantVO.setPoiNote(merchantEntity.getPoiNote());
        merchantVO.setRemark(merchantEntity.getRemark());
        merchantVO.setShopSign(merchantEntity.getShopSign());
        merchantVO.setOtherProof(merchantEntity.getOtherProof());
        merchantVO.setLastOrderTime(merchantEntity.getLastOrderTime());
        merchantVO.setAreaNo(merchantEntity.getAreaNo());
        merchantVO.setSize(merchantEntity.getSize());
        merchantVO.setType(merchantEntity.getType());
        merchantVO.setTradeArea(merchantEntity.getTradeArea());
        merchantVO.setTradeGroup(merchantEntity.getTradeGroup());
        merchantVO.setUnionid(merchantEntity.getUnionid());
        merchantVO.setMpOpenid(merchantEntity.getMpOpenid());
        merchantVO.setAdminId(merchantEntity.getAdminId());
        merchantVO.setDirect(merchantEntity.getDirect());
        merchantVO.setServer(merchantEntity.getServer());
        merchantVO.setPopView(merchantEntity.getPopView());
        merchantVO.setMemberIntegral(merchantEntity.getMemberIntegral());
        merchantVO.setGrade(merchantEntity.getGrade());
        merchantVO.setSkuShow(merchantEntity.getSkuShow());
        merchantVO.setRechargeAmount(merchantEntity.getRechargeAmount());
        merchantVO.setCashAmount(merchantEntity.getCashAmount());
        merchantVO.setCashUpdateTime(merchantEntity.getCashUpdateTime());
        merchantVO.setShowPrice(merchantEntity.getShowPrice());
        merchantVO.setMergeAdmin(merchantEntity.getMergeAdmin());
        merchantVO.setMergeTime(merchantEntity.getMergeTime());
        merchantVO.setFirstLoginPop(merchantEntity.getFirstLoginPop());
        merchantVO.setChangePop(merchantEntity.getChangePop());
        merchantVO.setPullBlackRemark(merchantEntity.getPullBlackRemark());
        merchantVO.setPullBlackOperator(merchantEntity.getPullBlackOperator());
        merchantVO.setHouseNumber(merchantEntity.getHouseNumber());
        merchantVO.setCompanyBrand(merchantEntity.getCompanyBrand());
        merchantVO.setCluePool(merchantEntity.getCluePool());
        merchantVO.setMerchantType(merchantEntity.getMerchantType());
        merchantVO.setEnterpriseScale(merchantEntity.getEnterpriseScale());
        merchantVO.setUpdateTime(merchantEntity.getUpdateTime());
        merchantVO.setExamineType(merchantEntity.getExamineType());
        merchantVO.setDisplayButton(merchantEntity.getDisplayButton());
        merchantVO.setOperateStatus(merchantEntity.getOperateStatus());
        merchantVO.setUpdater(merchantEntity.getUpdater());
        merchantVO.setDoorPic(merchantEntity.getDoorPic());
        merchantVO.setPreRegisterFlag(merchantEntity.getPreRegisterFlag());
        merchantVO.setIslock(merchantEntity.getIslock());

        return merchantVO;
    }



}
