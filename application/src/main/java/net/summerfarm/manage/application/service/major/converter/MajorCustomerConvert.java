package net.summerfarm.manage.application.service.major.converter;

import net.summerfarm.manage.application.inbound.controller.major.vo.MajorCustomerDetailVO;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/2/20 14:06
 */
@Mapper
public interface MajorCustomerConvert {

    MajorCustomerConvert INSTANCE = Mappers.getMapper(MajorCustomerConvert.class);

    MajorCustomerDetailVO buildMajorCustomerDetailVO(AdminEntity param);

}
