package net.summerfarm.manage.application.inbound.controller.customization.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 定制需求命令输入参数
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class CustomizationRequestCommandInput {

    /**
     * 主键Id（更新时使用）
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 设计效果图-设计师上传
     */
    private String designImage;
    /**
     * 设计师备注
     */
    private String designerRemark;

}
