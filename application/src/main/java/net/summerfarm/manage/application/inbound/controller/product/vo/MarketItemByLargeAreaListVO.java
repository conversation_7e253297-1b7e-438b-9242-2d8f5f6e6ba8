package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceLogVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class MarketItemByLargeAreaListVO extends MarketItemBaseVO implements Serializable {
    /**
     * sku类型 0, "常规"，1 "活动"，2 "临保"，3"拆包"， 4 "不卖"，5 "破袋"
     */
    private Integer extType;
    /**
     * 运营大区
     */
    private Integer largeAreaNo;
    /**
     * 运营大区
     */
    private String largeAreaName;
    /**
     * 产地
     */
    private String origin;
    /**
     * 保质期时长
     */
    private Integer qualityTime;
    /**
     * 采购成本 - 区间
     */
    private String cost;

    /**
     * 商城售价 - 区间
     */
    private String price;
    /**
     * 可用库存
     */
    private Integer availableStock;
    /**
     * 品种
     */
    private String levelPropertyName;
    /**
     * 级别
     */
    private String level;


    /**
     * 类目id
     */
    private Long categoryId;

    private BigDecimal minPrice;

    private BigDecimal maxPrice;

    private BigDecimal minCost;

    private BigDecimal maxCost;
    /**
     * 全路径类目名称
     */
    private String allPathCategoryName;
}
