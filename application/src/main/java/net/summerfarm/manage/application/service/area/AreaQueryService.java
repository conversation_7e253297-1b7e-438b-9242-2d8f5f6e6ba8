package net.summerfarm.manage.application.service.area;

import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.application.inbound.controller.area.input.LargeAreaQueryInput;
import net.summerfarm.manage.application.inbound.controller.area.vo.LargeAreaWithSubAreaVO;
import net.summerfarm.manage.application.service.area.dto.AreaSimpleDTO;

/**
 * @author: <EMAIL>
 * @create: 2023/12/8
 */
public interface AreaQueryService {

    /**
     * 根据区域编号查询区域信息
     * @param areaNos
     * @return
     */
    List<AreaSimpleDTO> batchQueryByAreaNos(List<Integer> areaNos);

    /**
     * 根据区域编号查询区域信息
     * @param largeAreaNos
     * @return
     */
    List<AreaSimpleDTO> batchQueryByLargeAreaNos(List<Integer> largeAreaNos);

    PageInfo<LargeAreaWithSubAreaVO> queryAllLargeAreas(LargeAreaQueryInput input);

    List<AreaSimpleDTO> batchQueryLargeAreaInfoByLargeAreaNos(List<Integer> largeAreaNos);
}
