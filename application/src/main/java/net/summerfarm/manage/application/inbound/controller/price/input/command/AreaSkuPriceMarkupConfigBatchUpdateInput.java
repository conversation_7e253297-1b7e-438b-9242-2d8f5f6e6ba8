package net.summerfarm.manage.application.inbound.controller.price.input.command;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */
@Data
public class AreaSkuPriceMarkupConfigBatchUpdateInput implements Serializable{
	/**
	 * 主键、自增
	 */
	@NotEmpty(message = "id列表不能为空")
	@Size(max = 50, message = "单次最多修改50条")
	private List<Long> ids;


	/**
	 * 加价类型：0-按金额加价，1-按百分比加价
	 */
	@NotNull(message = "markupType is not null")
	private Integer markupType;

	/**
	 * 加价金额/加价比例
	 */
	@NotNull(message = "markupValue is not null")
	private BigDecimal markupValue;



}