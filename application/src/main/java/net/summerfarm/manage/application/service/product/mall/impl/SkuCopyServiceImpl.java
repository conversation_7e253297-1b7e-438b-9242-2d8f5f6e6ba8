package net.summerfarm.manage.application.service.product.mall.impl;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.req.GoodsCodeInputReq;
import net.summerfarm.manage.application.service.product.converter.SkuCopyConverter;
import net.summerfarm.manage.application.service.product.mall.InventoryCommandService;
import net.summerfarm.manage.application.service.product.mall.SkuCopyService;
import net.summerfarm.manage.common.dto.SkuCopyDTO;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.entity.ProductsEntity;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.*;
import net.summerfarm.manage.domain.product.repository.*;
import net.summerfarm.manage.facade.fence.FenceQueryFacade;
import net.summerfarm.manage.facade.goods.GoodsFacade;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.warehouse.mapper.WarehouseInventoryMappingMapper;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SkuCopyServiceImpl implements SkuCopyService {

    @Resource
    private InventoryCommandRepository inventoryCRepostitory;
    @Resource
    private InventoryQueryRepository inventoryQRepostitory;

    @Resource
    private ProductsCommandRepository productsCRepostitory;
    @Resource
    private ProductsQueryRepository productsQRepostitory;

    @Resource
    private AreaSkuCommandRepository areaSkuCRepostitory;
    @Resource
    private AreaSkuQueryRepository areaSkuQRepostitory;

    @Resource
    private ProductsPropertyValueCommandRepository productsPropertyValueCRepostitory;
    @Resource
    private ProductsPropertyValueQueryRepository productsPropertyValueQRepository;

    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private InventoryCommandService inventoryCommandService;
    @Resource
    private GoodsFacade goodsFacade;
    @Resource
    private FenceQueryFacade fenceQueryFacade;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private WarehouseInventoryMappingMapper inventoryMappingMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SkuCopyDTO queryTemplateSkuAndSave(Long pdId, List<InventoryEntity> templateSkuList, Integer areaNo, String storeName) {
        SkuCopyDTO dto = new SkuCopyDTO ();
        //查询spu
        ProductsEntity products = productsQRepostitory.selectById (pdId);
        if (products == null) {
            throw new BizException ("spu不存在");
        }

        //查询areasku
        List<AreaSkuEntity> areaSkus = areaSkuQRepostitory.queryListSkuPrice (templateSkuList.stream ().map (InventoryEntity::getSku).collect (Collectors.toList ()),Collections.singletonList (areaNo),null);
        if (CollectionUtils.isEmpty(areaSkus)) {
            throw new BizException ("模版sku报价不存在");
        }

        if (areaSkus.size () < templateSkuList.size ()) {
            throw new BizException ("模版sku报价缺失");
        }
        Map<String, List<AreaSkuEntity>> areaSkuMap = areaSkus.stream ()
                .collect (Collectors.groupingBy (AreaSkuEntity::getSku));


        //查询属性
        List<ProductsPropertyValueEntity> productsPropertyValues = productsPropertyValueQRepository.selectByPdId (pdId);
        Map<String, List<ProductsPropertyValueEntity>> skuProValueMap = productsPropertyValues.stream ()
                .filter (e -> e.getSku () != null)
                .collect (Collectors.groupingBy (ProductsPropertyValueEntity::getSku));
        List<ProductsPropertyValueEntity> spuProValue = productsPropertyValues.stream ()
                .filter (e -> e.getSku () == null)
                .collect(Collectors.toList());


        //保存spu
        ProductsCommandParam params = SkuCopyConverter.productBuild(products,storeName);
        params.setPdNo (goodsFacade.takeSkuCode(GoodsCodeInputReq.builder()
                .categoryId(Long.valueOf(products.getCategoryId())).onlyTakeSpu(true).build()).getSpu());
        ProductsEntity newProducts = productsCRepostitory.insertSelective(params);
        Long newPdId = newProducts.getPdId();

        //保存spu属性
        if (!CollectionUtils.isEmpty(spuProValue)) {
            for (ProductsPropertyValueEntity spuProperty : spuProValue) {
                ProductsPropertyValueCommandParam propertyParam = SkuCopyConverter.propertyValueBuild(spuProperty);
                propertyParam.setPdId(newPdId);
                propertyParam.setSku(null); // SPU属性不关联SKU
                productsPropertyValueCRepostitory.insertSelective(propertyParam);
            }
        }

        Map<String, String> skuMapping = new HashMap<>();

        //保存sku
        for (InventoryEntity inventory : templateSkuList) {
            String newSku = goodsFacade.takeSkuCode (GoodsCodeInputReq.builder ().spu (newProducts.getPdNo ()).build ()).getSku ();
            //保存areasku
            areaSkuMap.get (inventory.getSku ()).forEach (areaSku -> {
                AreaSkuCommandParam areaSkuParam = SkuCopyConverter.areaSkuBuild(areaSku);
                areaSkuParam.setSku(newSku);
                areaSkuParam.setAreaNo(areaNo);
                areaSkuParam.setOnSale (true);
                areaSkuCRepostitory.insertSelective(areaSkuParam);
                log.info ("保存areasku:{},time={}", newSku,System.currentTimeMillis());
            });

            InventoryCommandParam inventoryParam = SkuCopyConverter.inventoryBuild(inventory,storeName);
            inventoryParam.setPdId(newPdId);
            inventoryParam.setSku(newSku); // 让系统生成新的SKU
            inventoryCRepostitory.insertSelective(inventoryParam);

            //保存sku属性
            List<ProductsPropertyValueEntity> skuProperties = skuProValueMap.get(inventory.getSku());
            if (!CollectionUtils.isEmpty(skuProperties)) {
                for (ProductsPropertyValueEntity skuProperty : skuProperties) {
                    ProductsPropertyValueCommandParam propertyParam = SkuCopyConverter.propertyValueBuild(skuProperty);
                    propertyParam.setPdId(newPdId);
                    propertyParam.setSku(newSku);
                    productsPropertyValueCRepostitory.insertSelective(propertyParam);
                }
            }

            // 新建sku初始化area_store信息
            areaStoreMapper.initAfterCreateSku(newSku, 1L);

            // 保存 WarehouseInventoryMapping
            List<Integer> storeNos = fenceQueryFacade.queryStoreNosByAreaNo (areaNo);
            if(CollectionUtil.isNotEmpty (storeNos)){
                List<WarehouseInventoryMapping> warehouseInventoryMappings = inventoryMappingMapper.listWareHouseNos (storeNos, inventory.getSku ());
                warehouseInventoryMappings.forEach (warehouseInventoryMapping -> {
                    WarehouseInventoryMapping mapping = new WarehouseInventoryMapping();
                    mapping.setWarehouseNo(warehouseInventoryMapping.getWarehouseNo ());
                    mapping.setStoreNo(warehouseInventoryMapping.getStoreNo ());
                    mapping.setSku(newSku);
                    mapping.setCreator(warehouseInventoryMapping.getCreator ());
                    mapping.setCreateTime(LocalDateTime.now());
                    this.inventoryMappingMapper.insertSelective(mapping);
                });
            }
            // 记录新旧SKU映射关系
            skuMapping.put(inventory.getSku(), newSku);
        }

        dto.setSkuMap (skuMapping);
        dto.setPdId (newPdId);
        return dto;
    }

    @Override
    public List<InventoryEntity> queryTemplateSku(Long pdId, String weightLike) {
        //查询sku
        List<InventoryEntity> inventories = inventoryQRepostitory.selectByPdIdAndWeigthLike (pdId, weightLike);
        if (CollectionUtils.isEmpty(inventories)) {
            throw new BizException ("模版sku不存在");
        }
        return inventories;
    }
}
