package net.summerfarm.manage.application.inbound.controller.contact.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description 配送周期查询
 * @date 2023/11/13 17:01:08
 */
@Data
public class ContactFrequencyQueryInput implements Serializable {

    /**
     *地址ID
     */
    @NotNull(message = "地址ID不能为空")
    private Long contactId;
}
