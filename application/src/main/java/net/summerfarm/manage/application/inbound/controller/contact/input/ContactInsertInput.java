package net.summerfarm.manage.application.inbound.controller.contact.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description 配送周期查询
 * @date 2023/11/13 17:01:08
 */
@Data
public class ContactInsertInput implements Serializable {

    /**
     *  商户id
     */
    @NotNull(message = "商户id不能为空")
    private List<Long> mIds;

    /**
     * 配送仓编号
     */
    @NotNull(message = "配送仓编号不能为空")
    private Integer storeNo;
}
