package net.summerfarm.manage.application.util;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.admin.assembler.AdminAssembler;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminVO;
import net.summerfarm.manage.common.valueobject.UserInfoValueObject;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.authentication.common.utils.SpringContextUtil;
import org.apache.shiro.SecurityUtils;

/**
 * @description 用户信息
 */
@Slf4j
public class UserInfoHolder {

    private static final ThreadLocal<AdminVO> ADMIN_ENTITY_THREAD_LOCAL = new ThreadLocal<>();

    private static final ThreadLocal<Set<Integer>> USER_ROLE_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 获取当前登录用户对象
     *
     * @return
     */
    public static AdminVO getCurrentUser() {
        if (ADMIN_ENTITY_THREAD_LOCAL.get() == null) {
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
            if (user == null) {
                log.warn("用户没有登录...");
                return null;
            }
            AdminQueryRepository bean = SpringContextUtil.getBean("adminQueryRepositoryImpl", AdminQueryRepository.class);
            AdminEntity admin = bean.selectByPrimaryKey(user.getBizUserId());
            if (admin == null) {
                log.warn("找不到用户:{}", JSON.toJSONString(user));
                return null;
            }
            ADMIN_ENTITY_THREAD_LOCAL.set(AdminAssembler.toAdminVO(admin));
        }
        return ADMIN_ENTITY_THREAD_LOCAL.get();
    }

    public static Set<Integer> getCurrentUserRoleIds() {
        if (null == USER_ROLE_THREAD_LOCAL.get()) {
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
            if (user != null) {
                USER_ROLE_THREAD_LOCAL.set(Sets.newHashSet(user.getRoleIds()));
            }
            log.error("用户没有任何Role:{}", JSON.toJSONString(user));
            return Collections.EMPTY_SET;
        }
        return USER_ROLE_THREAD_LOCAL.get();
    }

    /**
     * 获取当前登录用户id
     *
     * @return
     */
    public static Long getAdminId() {
        if (getCurrentUser() == null) {
            return null;
        }
        return getCurrentUser().getAdminId();
    }

    /**
     * 获取当前登录用户名称
     *
     * @return
     */
    public static String getAdminName() {
        if (getCurrentUser() == null) {
            return "系统默认";
        }
        return getCurrentUser().getRealname();
    }

    public static void clear() {
        ADMIN_ENTITY_THREAD_LOCAL.remove();
        USER_ROLE_THREAD_LOCAL.remove();
    }
}
