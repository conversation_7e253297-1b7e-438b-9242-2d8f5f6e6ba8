package net.summerfarm.manage.application.inbound.controller.merchant.assembler;


import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryInput;
import net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryParam;

/**
 *
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
public class MerchantFrequentlyBuyingSkuNotificationConfigAssembler {

    private MerchantFrequentlyBuyingSkuNotificationConfigAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static MerchantFrequentlyBuyingSkuNotificationConfigQueryParam toMerchantFrequentlyBuyingSkuNotificationConfigQueryParam(MerchantFrequentlyBuyingSkuNotificationConfigQueryInput merchantFrequentlyBuyingSkuNotificationConfigQueryInput) {
        if (merchantFrequentlyBuyingSkuNotificationConfigQueryInput == null) {
            return null;
        }
        MerchantFrequentlyBuyingSkuNotificationConfigQueryParam merchantFrequentlyBuyingSkuNotificationConfigQueryParam = new MerchantFrequentlyBuyingSkuNotificationConfigQueryParam();
        merchantFrequentlyBuyingSkuNotificationConfigQueryParam.setId(merchantFrequentlyBuyingSkuNotificationConfigQueryInput.getId());
        merchantFrequentlyBuyingSkuNotificationConfigQueryParam.setCreateTime(merchantFrequentlyBuyingSkuNotificationConfigQueryInput.getCreateTime());
        merchantFrequentlyBuyingSkuNotificationConfigQueryParam.setUpdateTime(merchantFrequentlyBuyingSkuNotificationConfigQueryInput.getUpdateTime());
        merchantFrequentlyBuyingSkuNotificationConfigQueryParam.setMId(merchantFrequentlyBuyingSkuNotificationConfigQueryInput.getMId());
        merchantFrequentlyBuyingSkuNotificationConfigQueryParam.setSpecialOffer(merchantFrequentlyBuyingSkuNotificationConfigQueryInput.getSpecialOffer());
        merchantFrequentlyBuyingSkuNotificationConfigQueryParam.setGoodsArrived(merchantFrequentlyBuyingSkuNotificationConfigQueryInput.getGoodsArrived());
        merchantFrequentlyBuyingSkuNotificationConfigQueryParam.setPageIndex(merchantFrequentlyBuyingSkuNotificationConfigQueryInput.getPageIndex());
        merchantFrequentlyBuyingSkuNotificationConfigQueryParam.setPageSize(merchantFrequentlyBuyingSkuNotificationConfigQueryInput.getPageSize());
        return merchantFrequentlyBuyingSkuNotificationConfigQueryParam;
    }
}
