package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2025/3/27 18:57
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryLevelVO {

    /**
     * 三级类目ID
     */
    private Long categoryId;
    /**
     * 三级类目名称
     */
    private String categoryName;
    /**
     * 二级类目名称
     */
    private String secondCategoryName;
    /**
     * 二级类目
     */
    private Long secondCategoryId;
    /**
     * 一级类目
     */
    private Long firstCategoryId;
    /**
     * 一级类目名称
     */
    private String firstCategoryName;

}
