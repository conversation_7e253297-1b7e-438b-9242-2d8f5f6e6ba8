package net.summerfarm.manage.application.service.product.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description
 * @Date 2024/7/30 11:43
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopSkuUpdateCommand {

    /**
     * sku编码
     */
    private String sku;
    /**
     * 主图
     */
    private String picturePath;
    /**
     * 详情图
     */
    private List<String> detailPictureList;
    /**
     * 视频链接
     */
    private String videoUrl;
    /**
     * 视频上传人
     */
    private String videoUploadUser;
}
