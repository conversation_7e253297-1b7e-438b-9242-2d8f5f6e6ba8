package net.summerfarm.manage.application.service.job;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.job.vo.JobProductVO;
import net.summerfarm.manage.application.inbound.controller.job.vo.MerchantJobDetailVo;
import net.summerfarm.manage.application.inbound.controller.job.vo.MerchantJobVo;
import net.xianmu.jobsdk.model.query.MerchantJobQuery;

public interface MerchantJobQueryService {

    /**
     * 分页查询任务列表
     */
    PageInfo<MerchantJobVo> pageJob(MerchantJobQuery query);

    /**
     * 获取任务详情
     */
    MerchantJobDetailVo getJobDetail(Long jobId);

    /**
     * 分页查询任务商品
     */
    PageInfo<JobProductVO> pageProduct(Long jobId, int pageIndex, int pageSize);

    /**
     * 导出生效中和已完成任务的任务进展
     *
     * @return resId
     */
    Long exportJobProgress(Long jobId, Long adminId);

}
