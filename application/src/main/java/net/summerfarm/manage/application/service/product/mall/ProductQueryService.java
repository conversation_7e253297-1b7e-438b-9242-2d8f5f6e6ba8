package net.summerfarm.manage.application.service.product.mall;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.product.input.MarketBaseQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductInfoInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductPageInput;
import net.summerfarm.manage.application.inbound.controller.product.input.SkuByLargeAreaQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.*;

/**
 * @author: <EMAIL>
 * @create: 2024/1/26
 */
public interface ProductQueryService {

    /**
     * 分页查询商品
     * @param input
     * @return
     */
    PageInfo<ProductVO> pageByQuery(ProductPageInput input);

    /**
     * 查询商品组合vo
     * @param input
     * @return
     */
    PageInfo<MarketItemAggListVO> pageAgg(MarketBaseQueryInput input);

    /**
     * 查询商品skuvo列表
     * @param input
     * @return
     */
    PageInfo<MarketItemListVO> pageSku(MarketBaseQueryInput input);
    /**
     * 后台商品管理查询
     * @param input
     * @return
     */
    PageInfo<ProductVO> selectPage(ProductPageInput input);
}
