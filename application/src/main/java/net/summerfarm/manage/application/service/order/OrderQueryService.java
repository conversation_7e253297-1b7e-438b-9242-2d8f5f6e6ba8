package net.summerfarm.manage.application.service.order;


import net.summerfarm.client.req.order.OrderDeliveryPlanDetailQueryReq;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanFlatObject;
import net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: 订单信息查询服务<br/>
 * date: 2024/6/5 13:59<br/>
 *
 * <AUTHOR> />
 */
public interface OrderQueryService {

    /**
     * 查询省心送代销不入仓未冻结订单数据
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<NoFreezeProxySaleNoWareNoSkuFlatObject> queryTimingOrderNoFreezeProxySaleNoWarehouse(LocalDate startDate, LocalDate endDate, Integer storeNo);


    /**
     * 根据城配仓查询省心送代销不入仓未设置的数量
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderNoSetOrderProxySaleNoWarehouseOrderInfo(Integer storeNo);

    /**
     * 查询有效的订单配送计划详情信息
     * @param req 请求
     * @return 结果
     */
    List<OrderDeliveryPlanFlatObject> queryValidOrderDeliveryPlanDetail(OrderDeliveryPlanDetailQueryReq req);
}
