package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.entity.ProductsEntity;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.*;


public class SkuCopyConverter{
    public static ProductsCommandParam productBuild(ProductsEntity products,String storeName) {
        if (products == null) {
            return null;
        }

        ProductsCommandParam param = new ProductsCommandParam();
        // 复制基本信息，但不复制pdId（让数据库自动生成新的ID）
        param.setCategoryId(products.getCategoryId());
        param.setBrandId(products.getBrandId());
        param.setPdName(products.getPdName()+storeName);
        param.setPddetail(products.getPddetail());
        param.setDetailPicture(products.getDetailPicture());
        param.setPriority(products.getPriority());
        param.setAfterSaleTime(products.getAfterSaleTime());
        param.setAfterSaleType(products.getAfterSaleType());
        param.setAfterSaleUnit(products.getAfterSaleUnit());
        param.setStorageLocation(products.getStorageLocation());
        param.setOrigin(products.getOrigin());
        param.setStorageMethod(products.getStorageMethod());
        param.setSlogan(products.getSlogan());
        param.setOtherSlogan(products.getOtherSlogan());
        param.setPicturePath(products.getPicturePath());
        param.setRefundType(products.getRefundType());
        param.setQualityTime(products.getQualityTime());
        param.setQualityTimeUnit(products.getQualityTimeUnit());
        param.setWarnTime(products.getWarnTime());
        param.setCreateType(products.getCreateType());
        param.setRealName(products.getRealName());
        param.setProductIntroduction(products.getProductIntroduction());
        param.setQualityTimeType(products.getQualityTimeType());
        return param;
    }

    /**
     * 构建库存命令参数
     */
    public static InventoryCommandParam inventoryBuild(InventoryEntity inventory,String storeName) {
        if (inventory == null) {
            return null;
        }

        InventoryCommandParam param = new InventoryCommandParam();
        param.setAitId(inventory.getAitId());
        param.setPdId(inventory.getPdId());
        param.setOrigin(inventory.getOrigin());
        param.setUnit(inventory.getUnit());
        param.setMaturity(inventory.getMaturity());
        param.setPack(inventory.getPack());
        param.setWeight(inventory.getWeight());
        param.setProductionDate(inventory.getProductionDate());
        param.setStorageMethod(inventory.getStorageMethod());
        param.setSalesMode(inventory.getSalesMode());
        param.setSalePrice(inventory.getSalePrice());
        param.setPromotionPrice(inventory.getPromotionPrice());
        param.setIntroduction(inventory.getIntroduction());
        param.setAfterSaleQuantity(inventory.getAfterSaleQuantity());
        param.setOutdated(inventory.getOutdated());
        param.setBaseSaleQuantity(inventory.getBaseSaleQuantity());
        param.setBaseSaleUnit(inventory.getBaseSaleUnit());
        param.setVolume(inventory.getVolume());
        param.setWeightNum(inventory.getWeightNum());
        param.setType(inventory.getType());
        param.setAdminId(inventory.getAdminId());
        param.setSamplePool(inventory.getSamplePool());
        param.setSkuPic(inventory.getSkuPic());
        param.setAfterSaleUnit(inventory.getAfterSaleUnit());
        param.setAddTime(inventory.getAddTime());
        param.setUpdateTime(inventory.getUpdateTime());
        param.setAuditStatus(inventory.getAuditStatus());
        param.setAuditTime(inventory.getAuditTime());
        param.setCreator(inventory.getCreator());
        param.setExtType(inventory.getExtType());
        param.setCreateRemark(inventory.getCreateRemark());
        param.setTaskType(inventory.getTaskType());
        param.setCreateType(inventory.getCreateType());
        param.setAuditor(inventory.getAuditor());
        param.setWeightNotes(inventory.getWeightNotes());
        param.setIsDomestic(inventory.getIsDomestic());
        param.setSupplierVisible(inventory.getSupplierVisible());
        param.setAveragePriceFlag(inventory.getAveragePriceFlag());
        param.setSkuName(inventory.getSkuName()+storeName);
        param.setRefuseReason(inventory.getRefuseReason());
        param.setTenantId(inventory.getTenantId());
        param.setSubType(inventory.getSubType());
        param.setShow(inventory.getShow());
        param.setRealName(inventory.getRealName());
        param.setBuyerId(inventory.getBuyerId());
        param.setBuyerName(inventory.getBuyerName());
        param.setNetWeightNum(inventory.getNetWeightNum());
        param.setNetWeightUnit(inventory.getNetWeightUnit());
        param.setVideoUrl(inventory.getVideoUrl());
        param.setAfterSaleRuleDetail(inventory.getAfterSaleRuleDetail());
        param.setVideoUploadUser(inventory.getVideoUploadUser());
        param.setVideoUploadTime(inventory.getVideoUploadTime());
        param.setQuoteType(inventory.getQuoteType());
        param.setMinAutoAfterSaleThreshold(inventory.getMinAutoAfterSaleThreshold());
        return param;
    }

    /**
     * 构建属性值命令参数
     */
    public static ProductsPropertyValueCommandParam propertyValueBuild(ProductsPropertyValueEntity propertyValue) {
        if (propertyValue == null) {
            return null;
        }

        ProductsPropertyValueCommandParam param = new ProductsPropertyValueCommandParam();
        // 复制基本信息，但不复制id（让系统生成新的）
        param.setProductsPropertyId(propertyValue.getProductsPropertyId());
        param.setProductsPropertyValue(propertyValue.getProductsPropertyValue());
        param.setCreator(propertyValue.getCreator());

        return param;
    }

    public static AreaSkuCommandParam areaSkuBuild(AreaSkuEntity areaSku) {
        if (areaSku == null) {
            return null;
        }

        AreaSkuCommandParam areaSkuCommandParam = new AreaSkuCommandParam();
        areaSkuCommandParam.setSku(areaSku.getSku());
        areaSkuCommandParam.setAreaNo(areaSku.getAreaNo());
        areaSkuCommandParam.setQuantity(areaSku.getQuantity());
        areaSkuCommandParam.setLockQuantity(areaSku.getLockQuantity());
        areaSkuCommandParam.setSafeQuantity(areaSku.getSafeQuantity());
        areaSkuCommandParam.setOriginalPrice(areaSku.getOriginalPrice());
        areaSkuCommandParam.setPrice(areaSku.getPrice());
        areaSkuCommandParam.setLadderPrice(areaSku.getLadderPrice());
        areaSkuCommandParam.setShare(areaSku.getShare());
        areaSkuCommandParam.setOnSale(areaSku.getOnSale());
        areaSkuCommandParam.setPriority(areaSku.getPriority());
        areaSkuCommandParam.setPdPriority(areaSku.getPdPriority());
        areaSkuCommandParam.setLimitedQuantity(areaSku.getLimitedQuantity());
        areaSkuCommandParam.setSalesMode(areaSku.getSalesMode());
        areaSkuCommandParam.setShow(areaSku.getShow());
        areaSkuCommandParam.setInfo(areaSku.getInfo());
        areaSkuCommandParam.setMType(areaSku.getMType());
        areaSkuCommandParam.setCornerStatus(areaSku.getCornerStatus());
        areaSkuCommandParam.setOpenSale(areaSku.getOpenSale());
        areaSkuCommandParam.setCloseSale(areaSku.getCloseSale());
        areaSkuCommandParam.setFixFlag(areaSku.getFixFlag());
        areaSkuCommandParam.setFixNum(areaSku.getFixNum());

        return areaSkuCommandParam;
    }
}
