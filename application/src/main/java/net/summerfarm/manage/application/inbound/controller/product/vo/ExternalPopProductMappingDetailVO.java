package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Date 2024/12/5 13:27
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalPopProductMappingDetailVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 鲜沐sku编码
     */
    private String xmSkuCode;

    /**
     * 区域最高价
     */
    private BigDecimal maxPrice;

    /**
     * 区域最低价
     */
    private BigDecimal minPrice;

    /**
     * 名称
     */
    private String title;

    /**
     * 规格
     */
    private String specification;

    /**
     * 毛重
     */
    private BigDecimal weightNum;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 毛重 斤
     */
    private BigDecimal weightNumJin;

    /**
     * 净重 斤
     */
    private BigDecimal netWeightNumJin;

    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 三级类目id
     */
    private Long categoryId;

    /**
     * 三级类目名称
     */
    private String categoryName;

    /**
     * 鲜沐售价
     */
    private String xmSkuSalePrice;

    /**
     * 外部商品类目名称
     */
    private String externalCategoryName;

    /**
     * 竞争对手的名称
     */
    private String competitor;

    /**
     * 商品的SKU编码（唯一标识）
     */
    private String externalSkuCode;

    /**
     * 数据爬取时间，格式为字符串
     */
    private String spiderFetchTime;

    /**
     * 商品的标准最终售价
     */
    private Double finalStandardPrice;

    /**
     * 月销量（最近一个月的销量）
     */
    private Long monthSale;

    /**
     * 商品详情页面的URL链接
     */
    private String url;

    /**
     * 商品的毛重（含包装）
     */
    private String grossWeight;

    /**
     * 商品的净重（不含包装）
     */
    private String netWeight;

    /**
     * 商品的规格描述
     */
    private String externalSpecification;

    /**
     * 商品名称
     */
    private String externalGoodsName;

    /**
     * 近三天的销量
     */
    private Long externalSalesVolume3d;

    /**
     * 月销量的成交总金额（GMV）
     */
    private Double externalMonthSaleGmv;

    /**
     * 商品所属的二级类目名称
     */
    private String externalCategoryLevel2;

    /**
     * 三天前的月销量成交总金额（GMV）
     */
    private Double externalMonthSaleGmv3dAgo;

    /**
     * 与当前商品匹配的Xianmu SKU列表（JSON格式或逗号分隔）
     */
    private String topMatchedXianmuSkuList;

    /**
     * 数据创建时间，格式为字符串
     */
    private String createTime;

    /**
     * 数据分区字段，通常表示日期（例如20240101）
     */
    private String ds;

    /**
     * 与当前商品匹配的Xianmu sku列表
     */
    private List<TopMatchedXianmuSkuVO> topMatchedXianmuSkuVOList;

    /**
     * 一级类目
     */
    private String externalCategory1;
    /**
     * 二级类目
     */
    private String externalCategory2;
    /**
     * 三级类目
     */
    private String externalCategory3;


    /**
     * 鲜沐成本
     */
    private BigDecimal xmSkuCost;

    /**
     * 鲜沐单斤成本
     */
    private BigDecimal xmSkuWeightCost;

    /**
     * 外部品成本
     */
    private BigDecimal externalSkuCost;

    /**
     * 外部单斤成本
     */
    private BigDecimal externalSkuGrossWeightCost;
}
