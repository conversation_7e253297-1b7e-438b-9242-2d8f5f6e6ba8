package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.input.ProductsPropertyInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductsPropertyVO;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyMappingCommandParam;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminName;

/**
 *
 * <AUTHOR>
 * @date 2024-05-07 16:18:00
 * @version 1.0
 *
 */
public class ProductsPropertyMappingConverter {

    public static List<ProductsPropertyMappingCommandParam> inputToCommonParam(List<ProductsPropertyInput> salePropertyList) {
        if (CollectionUtils.isEmpty(salePropertyList)) {
            return Collections.emptyList();
        }

        List<ProductsPropertyMappingCommandParam> params = new ArrayList<>(salePropertyList.size());
        for (ProductsPropertyInput productsPropertyInput : salePropertyList) {
            ProductsPropertyMappingCommandParam param = new ProductsPropertyMappingCommandParam();
            param.setProductsPropertyId(productsPropertyInput.getId());
            param.setCreator(getAdminName());
            params.add(param);
        }
        return params;
    }

    public static List<ProductsPropertyVO> entityToVOS(List<ProductsPropertyEntity> propertyEntityList) {
        if (CollectionUtils.isEmpty(propertyEntityList)) {
            return Collections.emptyList();
        }
        List<ProductsPropertyVO> productsPropertyVOS = new ArrayList<>();
        for (ProductsPropertyEntity productsPropertyEntity : propertyEntityList) {
            ProductsPropertyVO productsPropertyVO = new ProductsPropertyVO();
            productsPropertyVO.setId(productsPropertyEntity.getId());
            productsPropertyVO.setFormatStr(productsPropertyEntity.getFormatStr());
            productsPropertyVO.setName(productsPropertyEntity.getName());
            productsPropertyVO.setFormatType(productsPropertyEntity.getFormatType());
            productsPropertyVO.setType(productsPropertyEntity.getType());
            productsPropertyVOS.add(productsPropertyVO);
        }
        return productsPropertyVOS;
    }
}
