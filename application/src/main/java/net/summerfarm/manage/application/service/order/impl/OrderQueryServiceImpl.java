package net.summerfarm.manage.application.service.order.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.req.order.OrderDeliveryPlanDetailQueryReq;
import net.summerfarm.manage.application.service.order.OrderQueryService;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanQueryRepository;
import net.summerfarm.manage.domain.delivery.service.DeliveryQueryDomainService;
import net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanFlatObject;
import net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject;
import net.summerfarm.manage.domain.order.service.OrdersQueryDomainService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 订单查询服务<br/>
 * date: 2024/6/5 14:27<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class OrderQueryServiceImpl implements OrderQueryService {

    @Resource
    private DeliveryQueryDomainService deliveryQueryDomainService;
    @Resource
    private OrdersQueryDomainService ordersQueryDomainService;
    @Resource
    private DeliveryPlanQueryRepository deliveryPlanQueryRepository;

    @Override
    public List<NoFreezeProxySaleNoWareNoSkuFlatObject> queryTimingOrderNoFreezeProxySaleNoWarehouse(LocalDate startDate, LocalDate endDate, Integer storeNo) {
        if(startDate == null || endDate == null || storeNo == null){
            throw new BizException("开始时间、结束时间、城配仓编号均不能为空");
        }
        return deliveryQueryDomainService.queryTimingOrderNoFreezeProxySaleNoWarehouse(startDate,endDate,storeNo);
    }

    @Override
    public List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderNoSetOrderProxySaleNoWarehouseOrderInfo(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号均不能为空");
        }
        //根据城配仓查询省心送代销不入仓订单购买SKU的总数量
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuTotalNumList = ordersQueryDomainService.queryTimingOrderProxySaleNoWarehouseSkuTotalNum(storeNo);

        //根据城配仓查询省心送代销不入仓已设置的代销不如仓SKU的数量
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuHaveSetNumList = ordersQueryDomainService.queryTimingOrderProxySaleNoWarehouseHaveSetSkuNum(storeNo);

        //根据城配仓查询省心送代销不入仓售后的SKU的数量
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> afterSaleSkuNumList = ordersQueryDomainService.queryTimingOrderProxySaleNoWarehouseAfterSaleSkuNum(storeNo);

        Map<String, Integer> storeNoSku2SkuNumMap = skuTotalNumList.stream().collect(Collectors.toMap(info -> info.getStoreNo() + "#" + info.getSku(), info -> info.getQuantity()));
        Map<String, Integer> storeNoSku2SkuHaveSetNumMap = skuHaveSetNumList.stream().collect(Collectors.toMap(info -> info.getStoreNo() + "#" + info.getSku(), info -> info.getQuantity()));
        Map<String, Integer> storeNoSku2SkuAfterSaleNumMap = afterSaleSkuNumList.stream().collect(Collectors.toMap(info -> info.getStoreNo() + "#" + info.getSku(), info -> info.getQuantity()));

        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> resultList = new ArrayList<>();

        for (String storeNoSkuKey : storeNoSku2SkuNumMap.keySet()) {
            //指定只分隔一次
            String[] storeNoSku = storeNoSkuKey.split("#",2);
            if(storeNoSku.length != 2){
                log.error("storeNoSkuKey：{}，分隔后长度不为2,城配仓、Sku数据存在问题", storeNoSkuKey);
                continue;
            }
            String storeNoStr = storeNoSku[0];
            String skuStr = storeNoSku[1];


            Integer skuTotalNum = storeNoSku2SkuNumMap.get(storeNoSkuKey) == null ? 0 : storeNoSku2SkuNumMap.get(storeNoSkuKey);
            Integer skuHaveSetNum = storeNoSku2SkuHaveSetNumMap.get(storeNoSkuKey) == null ? 0 : storeNoSku2SkuHaveSetNumMap.get(storeNoSkuKey);
            Integer skuAfterSaleNum = storeNoSku2SkuAfterSaleNumMap.get(storeNoSkuKey) == null ? 0 : storeNoSku2SkuAfterSaleNumMap.get(storeNoSkuKey);

            //未设置的省心送数量=订单总数-已设置数量-未到货退款售后数量
            Integer noSetSkuNum = skuTotalNum - skuHaveSetNum - skuAfterSaleNum;

            log.info("storeNoSkuKey:{},未设置的省心送数量:{},订单总数:{},已设置数量:{},未到货退款售后数量:{}",
                    storeNoSkuKey, noSetSkuNum,skuTotalNum,skuHaveSetNum,skuAfterSaleNum);

            TimingOrderProxySaleNoWarehouseSkuFlatObject skuResult = new TimingOrderProxySaleNoWarehouseSkuFlatObject();
            skuResult.setSku(skuStr);
            skuResult.setStoreNo(Integer.parseInt(storeNoStr));
            skuResult.setNotSetQuantity(noSetSkuNum);

            resultList.add(skuResult);
        }

        return resultList;
    }

    @Override
    public List<OrderDeliveryPlanFlatObject> queryValidOrderDeliveryPlanDetail(OrderDeliveryPlanDetailQueryReq req) {
        List<String> orderNoList = req.getOrderNoList();
        if(orderNoList == null || orderNoList.isEmpty()){
            return Collections.emptyList();
        }
        // 查询配送计划
        return deliveryPlanQueryRepository.queryValidOrderDeliveryPlanDetail(req.getDeliveryTime(),req.getOrderNoList());
    }
}
