package net.summerfarm.manage.application.inbound.controller.product.handler;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoTopSaleSkuQueryParam;
import net.summerfarm.manage.domain.product.repository.AppPopBiaoguoTopSaleSkuQueryRepository;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import org.apache.commons.compress.utils.Lists;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2024/11/27 15:00
 * @<AUTHOR>
 */
@Slf4j
@Component
public class ExternalProductHandler {

    @Resource
    private AppPopBiaoguoTopSaleSkuQueryRepository topSaleSkuQueryRepository;
    @Resource
    private InventoryQueryRepository inventoryQueryRepository;

    public List<TopMatchedCompetitorSkuEntity> parseTopMatchedCompetitorSku(AppPopTopMatchedCompetitorSkuListEntity appPopTopMatchedCompetitorSkuListEntity) {
        if (Objects.isNull(appPopTopMatchedCompetitorSkuListEntity)
                || StringUtils.isBlank(appPopTopMatchedCompetitorSkuListEntity.getTopMatchedCompetitorSkuList())) {
            return Lists.newArrayList();
        }
        try {
            List<TopMatchedCompetitorSkuEntity> competitorSkuEntityList = JSON.parseArray((String) JSON.parse(appPopTopMatchedCompetitorSkuListEntity.getTopMatchedCompetitorSkuList()), TopMatchedCompetitorSkuEntity.class);
            if (CollectionUtils.isEmpty(competitorSkuEntityList)) {
                return Lists.newArrayList();
            }
            List<String> competitorSkuCode = competitorSkuEntityList.stream().map(TopMatchedCompetitorSkuEntity::getCompetitorSkuCode).distinct().collect(Collectors.toList());
            // 补充毛重 净重 规格描述等信息
            AppPopBiaoguoTopSaleSkuQueryParam queryParam = new AppPopBiaoguoTopSaleSkuQueryParam();
            queryParam.setSkuCodeList(competitorSkuCode);
            List<AppPopBiaoguoTopSaleSkuEntity> topSaleSkuEntityList = topSaleSkuQueryRepository.selectByCondition(queryParam);
            Map<String, AppPopBiaoguoTopSaleSkuEntity> topSaleSkuEntityMap = topSaleSkuEntityList.stream()
                    .collect(Collectors.toMap(AppPopBiaoguoTopSaleSkuEntity::getSkuCode, Function.identity(), (a, b) -> a));
            competitorSkuEntityList.forEach(competitorSkuVO -> {
                AppPopBiaoguoTopSaleSkuEntity topSaleSkuEntity = topSaleSkuEntityMap.get(competitorSkuVO.getCompetitorSkuCode());
                if (Objects.isNull(topSaleSkuEntity)) {
                    return;
                }
                competitorSkuVO.setSpecification(topSaleSkuEntity.getSpecification());
                competitorSkuVO.setGrossWeight(topSaleSkuEntity.getGrossWeight());
                competitorSkuVO.setNetWeight(topSaleSkuEntity.getNetWeight());
            });

            return competitorSkuEntityList.stream().sorted(Comparator.comparing(TopMatchedCompetitorSkuEntity::getSimilarityScore).reversed()).collect(Collectors.toList());

        } catch (Exception e) {
            log.warn("convert error, topMatchedCompetitorSkuListEntity:{}, e", JSON.toJSONString(appPopTopMatchedCompetitorSkuListEntity), e);
            return Lists.newArrayList();
        }
    }

    public List<TopMatchedXianmuSkuEntity> parseTopMatchedXmSkuEntity(AppPopBiaoguoTopSaleSkuEntity topSaleSkuEntity) {
        if (Objects.isNull(topSaleSkuEntity)
                || StringUtils.isBlank(topSaleSkuEntity.getTopMatchedXianmuSkuList())) {
            return Lists.newArrayList();
        }
        try {
            List<TopMatchedXianmuSkuEntity> topMatchedXianmuSkuEntityList = JSON.parseArray((String) JSON.parse(topSaleSkuEntity.getTopMatchedXianmuSkuList()), TopMatchedXianmuSkuEntity.class);
            if (CollectionUtils.isEmpty(topMatchedXianmuSkuEntityList)) {
                return Lists.newArrayList();
            }
            List<String> xmSkuCodeList = topMatchedXianmuSkuEntityList.stream().map(TopMatchedXianmuSkuEntity::getXianmuSkuCode).distinct().collect(Collectors.toList());
            // 补充毛重 净重 规格描述等信息
            List<InventoryEntity> inventoryEntityList = inventoryQueryRepository.listBySkus(xmSkuCodeList);
            Map<String, InventoryEntity> topSaleSkuEntityMap = inventoryEntityList.stream()
                    .collect(Collectors.toMap(InventoryEntity::getSku, Function.identity(), (a, b) -> a));
            topMatchedXianmuSkuEntityList.forEach(matchedXianmuSku -> {
                InventoryEntity inventoryEntity = topSaleSkuEntityMap.get(matchedXianmuSku.getXianmuSkuCode());
                if (Objects.isNull(inventoryEntity)) {
                    return;
                }
                matchedXianmuSku.setSpecification(inventoryEntity.getWeight());
                matchedXianmuSku.setNetWeightNum(inventoryEntity.getNetWeightNum());
                matchedXianmuSku.setWeightNum(inventoryEntity.getWeightNum());
            });
            return topMatchedXianmuSkuEntityList.stream().sorted(Comparator.comparing(TopMatchedXianmuSkuEntity::getSimilarityScore).reversed()).collect(Collectors.toList());

        } catch (Exception e) {
            log.warn("convert error, topSaleSkuEntity:{}, e", JSON.toJSONString(topSaleSkuEntity), e);
            return Lists.newArrayList();
        }
    }

}
