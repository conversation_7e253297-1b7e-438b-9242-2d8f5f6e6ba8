package net.summerfarm.manage.application.inbound.controller.customization.assembler;

import net.summerfarm.client.req.customization.CustomizationRequestReq;
import net.summerfarm.manage.application.inbound.controller.customization.input.CustomizationRequestCommandInput;
import net.summerfarm.manage.application.inbound.controller.customization.input.CustomizationRequestQueryInput;
import net.summerfarm.manage.application.inbound.controller.customization.vo.CustomizationRequestRelationOrderVO;
import net.summerfarm.manage.application.inbound.controller.customization.vo.CustomizationRequestVO;
import net.summerfarm.manage.common.enums.ArrivalStatusEnum;
import net.summerfarm.manage.domain.customization.entity.CustomizationRequestEntity;
import net.summerfarm.manage.domain.customization.param.CustomizationRequestQueryParam;
import net.summerfarm.manage.domain.order.flatObject.CustomOrderInventoryInfoDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 定制需求组装器
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public class CustomizationRequestAssembler {

    /**
     * 命令输入转实体
     */
    public static CustomizationRequestEntity commandInputToEntity(CustomizationRequestReq input) {
        if (input == null) {
            return null;
        }
        
        CustomizationRequestEntity entity = new CustomizationRequestEntity();
        entity.setStoreName(input.getStoreName());
        entity.setAccountName(input.getAccountName());
        entity.setMId(input.getMId());
        entity.setColorCount(input.getColorCount());
        entity.setReferenceImage(input.getReferenceImage());
        entity.setLogoImage(input.getLogoImage());
        entity.setLogoSize(input.getLogoSize());
        entity.setSampleImage(input.getSampleImage());
        entity.setRefuseReason(input.getRefuseReason());
        entity.setStoreRemark(input.getStoreRemark());
        return entity;
    }

    /**
     * 查询输入转实体
     */
    public static CustomizationRequestQueryParam queryInputToParam(CustomizationRequestQueryInput input) {
        if (input == null) {
            return null;
        }

        CustomizationRequestQueryParam param = new CustomizationRequestQueryParam();
        param.setId(input.getId());
        param.setStoreName(input.getStoreName());
        param.setStatus(input.getStatus());
        
        return param;
    }

    /**
     * 实体转VO
     */
    public static CustomizationRequestVO entityToVO(CustomizationRequestEntity entity) {
        if (entity == null) {
            return null;
        }
        
        CustomizationRequestVO vo = new CustomizationRequestVO();
        vo.setId(entity.getId());
        vo.setStoreName(entity.getStoreName());
        vo.setAccountName(entity.getAccountName());
        vo.setMId(entity.getMId());
        vo.setColorCount(entity.getColorCount());
        vo.setDesignSubmitTime(entity.getDesignSubmitTime());
        vo.setReferenceImage(entity.getReferenceImage());
        vo.setLogoImage(entity.getLogoImage());
        vo.setLogoSize(entity.getLogoSize());
        vo.setSampleImage(entity.getSampleImage());
        vo.setDesignImage(entity.getDesignImage());
        vo.setStatus(entity.getStatus());
        vo.setRefuseReason(entity.getRefuseReason());
        vo.setStoreRemark(entity.getStoreRemark());
        vo.setDesignerRemark(entity.getDesignerRemark());
        return vo;
    }

    /**
     * 实体列表转VO列表
     */
    public static List<CustomizationRequestVO> entityListToVOList(List<CustomizationRequestEntity> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
                .map(CustomizationRequestAssembler::entityToVO)
                .collect(Collectors.toList());
    }

    public static CustomizationRequestRelationOrderVO customOrderInventoryInfoDTO2VO(CustomOrderInventoryInfoDTO i) {
        CustomizationRequestRelationOrderVO customizationRequestRelationOrderVO = new CustomizationRequestRelationOrderVO ();
        customizationRequestRelationOrderVO.setOrderNo(i.getOrderNo());
        customizationRequestRelationOrderVO.setPdName(i.getPdName());
        customizationRequestRelationOrderVO.setWeight(i.getWeight());
        customizationRequestRelationOrderVO.setSku(i.getSkuCode());
        customizationRequestRelationOrderVO.setAmount(i.getOrderQuantity());
        customizationRequestRelationOrderVO.setArrivalStatus(getArrivalStatus(i.getOrderQuantity (), i.getSaleInventoryQuantity()));
        return customizationRequestRelationOrderVO;
    }

    private static Integer getArrivalStatus(Integer orderQuantity, Integer saleInventoryQuantity) {
        // 是否到货 0 未到货 1 已到货 2部分发货
        if(saleInventoryQuantity>=orderQuantity){
            return ArrivalStatusEnum.ALL_ARRIVAL.getValue ();
        }else if(0<saleInventoryQuantity && saleInventoryQuantity<orderQuantity){
            return ArrivalStatusEnum.PART_ARRIVAL.getValue ();
        }else{
            return ArrivalStatusEnum.NO_ARRIVAL.getValue ();
        }
    }
}
