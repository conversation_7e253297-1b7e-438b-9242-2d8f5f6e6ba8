package net.summerfarm.manage.application.service.dts;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import net.summerfarm.manage.application.service.job.CrmJobMerchantDetailCommandService;
import net.summerfarm.manage.application.service.order.OrderCommandService;
import net.summerfarm.manage.common.dto.DtsModel;
import net.summerfarm.manage.common.dto.XmPair;
import net.summerfarm.manage.common.enums.dts.DtsModelTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @ClassName MarketCouponSendDetailDmlImpl
 * @Description 未到货售后删除参与满返订单优惠券（支付完成后发放）
 * <AUTHOR>
 * @Date 19:00 2024/4/26
 * @Version 1.0
 **/
@Slf4j
@Service
public class AfterSaleOrderDmlImpl extends AbstractDbTableDml {

    @Resource
    private OrderCommandService orderCommandService;
    @Resource
    private CrmJobMerchantDetailCommandService crmJobMerchantDetailCommandService;

    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        if (Objects.equals(dtsModel.getType(), DtsModelTypeEnum.UPDATE.name())) {
            // 未到货售后完成后处理任务
            this.handlerMerchantJob(pair);

            //判断是否未到货售后
            Map<String, String> data = pair.getKey();
            String deliveryed = data.get("deliveryed");
            String status = data.get("status");
            if (Objects.equals(deliveryed, "0") && Objects.equals(status, "2")) {
                String orderNo = data.get("order_no");
                String sku = data.get("sku");
                Long mId = Long.valueOf(data.get("m_id"));
                orderCommandService.deleteOrderFullReturnCoupon(orderNo, sku, mId);
            }
        }
    }


    // 未到货售后处理门店任务
    private void handlerMerchantJob(XmPair<Map<String, String>, Map<String, String>> pair) {
        try {
            log.info("【发生售后门店任务处理】开始处理当前可能会无法达标的任务。pair：{}", JSON.toJSONString(pair));
            Map<String, String> data = pair.getKey();
            Map<String, String> old = pair.getValue();
            String deliveryed = data.get("deliveryed");
            String status = data.get("status");
            Integer oldOrderStatus = Optional.ofNullable(old.get("status")).map(Integer::valueOf).orElse(null);
            if(oldOrderStatus == null) {
                log.info("【发生售后门店任务处理】售后单状态未发生变化");
                return;
            }
            // 1. 只处理未到货售后
            // 2. 售后状态变成“已完成” 的
            if (Objects.equals(deliveryed, "0") && Objects.equals(status, "2")) {
                String orderNo = data.get("order_no");
                String afterSaleNo = data.get("after_sale_order_no");
                crmJobMerchantDetailCommandService.updateJobAfterSale(afterSaleNo, orderNo);
            }
        } catch (Exception e) {
            log.info("发生未到货售后处理任务失败。data：{}", JSON.toJSONString(pair), e);
        }
    }
}
