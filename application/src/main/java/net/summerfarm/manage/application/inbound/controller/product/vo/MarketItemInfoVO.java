package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品sku详情
 */
@Data
public class MarketItemInfoVO extends MarketItemBaseVO implements Serializable {
    /**
     * 产地
     */
    private String origin;
    /**
     * 图片
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 品牌Id
     */
    private Long brandId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 无货商品供应价
     */
    private BigDecimal noGoodsSupplyPrice;

    /**
     * 供应商Id
     */
    private String supplierId;
    /**
     * 最大售后数
     */
    private Integer maxAfterSaleAmount;
    /**
     * 售后单位
     */
    private String afterSaleUnit;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价 3组合品按总价下调固定额度,4组合品按总价下调百分比 5组合品总价
     */
    private Integer priceType;
    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;
}