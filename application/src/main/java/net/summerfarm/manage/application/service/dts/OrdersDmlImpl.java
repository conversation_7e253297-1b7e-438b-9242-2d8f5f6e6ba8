package net.summerfarm.manage.application.service.dts;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.application.inbound.mq.msgbody.WeixinShippingDTO;
import net.summerfarm.manage.application.service.job.CrmJobMerchantDetailCommandService;
import net.summerfarm.manage.application.service.order.OrderCommandService;
import net.summerfarm.manage.application.service.wx.WeixinShippingServiceImpl;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.dto.DtsModel;
import net.summerfarm.manage.common.dto.XmPair;
import net.summerfarm.manage.common.enums.OrderStatusEnum;
import net.summerfarm.manage.common.enums.dts.DtsModelTypeEnum;
import net.summerfarm.manage.domain.order.entity.OrderRelationEntity;
import net.summerfarm.manage.domain.order.repository.OrderRelationQueryRepository;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 订单binlog监听
 * 1. pop订单发货后同步信息到微信
 *
 * @Date 19:00 2024/4/26
 * @Version 1.0
 **/
@Slf4j
@Service
public class OrdersDmlImpl extends AbstractDbTableDml {

    @Resource
    private OrderRelationQueryRepository orderRelationQueryRepository;
    @Resource
    private CrmJobMerchantDetailCommandService crmJobMerchantDetailCommandService;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;
    @Resource
    private MqProducer mqProducer;

    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        if (Objects.equals(dtsModel.getType(), DtsModelTypeEnum.UPDATE.name())) {
            this.handlerMerchantJob(pair);
            this.uploadShippingInfo(pair);
        } else {
            log.info("暂时无需处理的操作类型");
        }
    }


    // 支付完成处理门店任务
    private void handlerMerchantJob(XmPair<Map<String, String>, Map<String, String>> pair) {
        try {
            log.info("【门店任务处理】开始处理当前订单可能会达成的任务。pair：{}", JSON.toJSONString(pair));
            Map<String, String> data = pair.getKey();
            Map<String, String> old = pair.getValue();
            Integer orderStatus = Optional.ofNullable(data.get("status")).map(Integer::valueOf).orElse(null);
            Integer oldOrderStatus = Optional.ofNullable(old.get("status")).map(Integer::valueOf).orElse(null);
            String orderNo = data.get("order_no");
            if (oldOrderStatus == null) {
                log.info("【门店任务处理】订单状态未发生变化");
                return;
            }
            if (!Objects.equals(oldOrderStatus, OrderStatusEnum.NO_PAYMENT.getId())) {
                log.info("【门店任务处理】原订单不是待支付状态!");
                return;
            }
            if (!Objects.equals(orderStatus, OrderStatusEnum.DELIVERING.getId())) {
                log.info("【门店任务处理】当前订单不是待收货状态!");
                return;
            }
            // 判断该订单能达成的任务
            crmJobMerchantDetailCommandService.finishJobAfterPaymentSucceeded(orderNo);
        } catch (Exception e) {
            log.error("【门店任务处理】处理失败。pair：{}", JSON.toJSONString(pair), e);
        }
    }


    private void uploadShippingInfo(XmPair<Map<String, String>, Map<String, String>> pair) {
        try {
            log.info("【小程序发货信息管理】开始处理上传订单信息到微信的请求。pair：{}", JSON.toJSONString(pair));
            if (!nacosPropertiesHolder.getWeixinShippingSwitch()) {//NOSONAR
                log.info("【小程序发货信息管理】开关为关闭状态，不做处理");
                return;
            }
            Map<String, String> data = pair.getKey();
            Map<String, String> old = pair.getValue();
            Integer orderStatus = Optional.ofNullable(data.get("status")).map(Integer::valueOf).orElse(null);
            Integer oldOrderStatus = Optional.ofNullable(old.get("status")).map(Integer::valueOf).orElse(null);
            Integer ordersType = Optional.ofNullable(data.get("type")).map(Integer::valueOf).orElse(null);

            if (ordersType == null) {
                log.info("【小程序发货信息管理】订单类型为空，暂不处理");
                return;
            }
            if (oldOrderStatus == null) {
                log.info("【小程序发货信息管理】订单状态未发生变化");
                return;
            }
            if (!Objects.equals(oldOrderStatus, OrderStatusEnum.NO_PAYMENT.getId())) {
                log.info("【小程序发货信息管理】原订单不是待支付状态!");
                return;
            }
            if (!Objects.equals(orderStatus, OrderStatusEnum.DELIVERING.getId())
                    && !Objects.equals(orderStatus, OrderStatusEnum.WAIT_DELIVERY.getId())
                    && !Objects.equals(orderStatus, OrderStatusEnum.RECEIVED.getId())) {
                log.info("【小程序发货信息管理】当前订单不是待收货/待配送/已收货状态!");
                return;
            }
            String orderNo = data.get("order_no");
            if (StringUtils.isBlank(orderNo)) {
                log.info("【小程序发货信息管理】订单数据异常,订单号为空!");
                return;
            }


            WeixinShippingDTO dto = new WeixinShippingDTO();
            dto.setOrderNo(orderNo);
            dto.setOrdersType(ordersType);
            Long delayTime = nacosPropertiesHolder.getWeixinShippingDelayTime() * 1000;
            mqProducer.sendDelay("topic_sf_mall_manage_common", "tag_wei_xin_shipping", dto, delayTime);
            log.info("【小程序发货信息管理】上传订单信息到微信完成!");
        } catch (Exception e) {
            log.error("【小程序发货信息管理】处理失败。pair：{}", JSON.toJSONString(pair), e);
        }
    }
}
