package net.summerfarm.manage.application.inbound.controller.marketItem;

import net.summerfarm.manage.application.inbound.controller.marketItem.input.query.MarketItemAiExtGoodsQueryInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.vo.MarketItemAiExtGoodsVO;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping(value="/marketItemAI4ProductInfo")
public class MarketItemAI4ProductInfoController {

    @Resource
    private MarketItemAiExtCommandService marketItemAiExtCommandService;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    /**
     * 查询商品信息
     * @param input 商品搜索信息
     *                skus 商品编码列表
     * @return
     */
    @PostMapping(value = "/getProductInfoTextBySkus")
    public CommonResult<MarketItemAiExtGoodsVO> getProductInfoTextBySkus(@RequestBody MarketItemAiExtGoodsQueryInput input){
        if (!nacosPropertiesHolder.getMarketItemAI4ProductInfoSsKey().equals(input.getSsKey())){
            throw new IllegalArgumentException("ssKey is not valid");
        }

        Map<String, String> goodsInfoMap = marketItemAiExtCommandService.getProductInfoTextBySkus(input.getSkus());

        MarketItemAiExtGoodsVO marketItemAiExtGoodsVO = new MarketItemAiExtGoodsVO();
        marketItemAiExtGoodsVO.setGoodsInfoMap(goodsInfoMap);
        return CommonResult.ok(marketItemAiExtGoodsVO);
    }

    /**
     * 查询商品信息
     * @param input 商品搜索信息
     *                skus 商品编码列表
     * @return
     */
    @PostMapping(value = "/getSearchKeywordsBySkus")
    public CommonResult<MarketItemAiExtGoodsVO> getSearchKeywordsBySkus(@RequestBody MarketItemAiExtGoodsQueryInput input){
        if (!nacosPropertiesHolder.getMarketItemAI4ProductInfoSsKey().equals(input.getSsKey())){
            throw new IllegalArgumentException("ssKey is not valid");
        }

        Map<String, String> goodsInfoMap = marketItemAiExtCommandService.getSearchKeywordsBySkus(input.getSkus());

        MarketItemAiExtGoodsVO marketItemAiExtGoodsVO = new MarketItemAiExtGoodsVO();
        marketItemAiExtGoodsVO.setGoodsInfoMap(goodsInfoMap);
        return CommonResult.ok(marketItemAiExtGoodsVO);
    }
}
