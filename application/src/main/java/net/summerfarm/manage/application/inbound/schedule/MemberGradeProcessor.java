package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.member.MemberCommandService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/2/1  14:31
 */
@Component
@Slf4j
public class MemberGradeProcessor  extends XianMuJavaProcessorV2 {
    @Resource
    MemberCommandService memberCommandService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("月初更新积分开始");
        memberCommandService.calculateMemberGrade();
        log.info("月初更新积分结束");
        return new ProcessResult(true);
    }
}
