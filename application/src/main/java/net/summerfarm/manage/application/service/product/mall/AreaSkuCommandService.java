package net.summerfarm.manage.application.service.product.mall;

import net.summerfarm.manage.application.inbound.mq.msgbody.AreaSkuPriceDTO;

import java.util.List;
import java.util.Set;

public interface AreaSkuCommandService {
    Integer updateOrAddAreaSkuPrice(AreaSkuPriceDTO areaSkuPriceDTO);

    /**
     *
     * 更新鲜果pop的sku上下架状态
     *
     * @param sku
     * @param warehouseNo
     * @param onsale
     * @return
     */
    Integer updateAreaSkuOnSale(String sku, Integer warehouseNo, boolean onsale);
    void updateAreaSkuOnSaleBatch(String sku, Set<Integer> areaNos, boolean onsale);

    void offSaleByIds(List<Integer> ids);

    void offSaleBySkus(List<String> skus);
}
