package net.summerfarm.manage.application.service.account;

import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantSubAccountVO;

import java.util.List;

public interface MerchantAccountService {
    /**
     * 根据storeId获取 门店账号信息
     * @param storeId
     * @return
     */
    List<MerchantSubAccountVO> getByStoreId(Long storeId);

    /**
     *  根据mId获取 门店账号信息
     * @param mId
     * @return
     */
    List<MerchantSubAccountVO> getByMId(Long mId);

}
