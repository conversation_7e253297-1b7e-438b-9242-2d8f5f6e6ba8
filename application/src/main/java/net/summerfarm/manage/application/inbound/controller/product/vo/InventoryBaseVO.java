package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * @Date 2025/1/14 15:19
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBaseVO {

    /**
     * sku编码
     */
    private String sku;

    /**
     * pdId
     */
    private Long pdId;

    /**
     * pdNo
     */
    private String pdNo;

    /**
     * 规格
     */
    private String weight;

    /**
     * 包装
     */
    private String unit;

    /**
     * 性质
     * 类型 0 自营 1 代仓
     */
    private Integer type;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 租户ID：1-鲜沐
     */
    private Long tenantId;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 净重单位
     */
    private String netWeightUnit;

    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 全路径类目名称
     */
    private String allPathCategoryName;

    /**
     * 橱窗图
     */
    private String picturePath;
    /**
     * 详情图
     */
    private List<String> detailPictureList;

    /**
     * 果规
     */
    private String fruitSize;

    /**
     * 类目信息
     */
    private CategoryLevelVO goodsCategoryVO;

    /**
     * 商品状态
     */
    private Integer outdated;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 自定义属性值
     */
    private List<ProductsPropertyValueVO> productsPropertyValueVOList;


}
