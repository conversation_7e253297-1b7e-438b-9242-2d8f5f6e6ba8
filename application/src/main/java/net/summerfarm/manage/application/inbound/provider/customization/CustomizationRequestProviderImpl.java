package net.summerfarm.manage.application.inbound.provider.customization;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.customization.CustomizationRequestProvider;
import net.summerfarm.client.req.customization.CustomizationRequestReq;
import net.summerfarm.manage.application.service.customization.CustomizationRequestService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

@Slf4j
@DubboService
@Component
public class CustomizationRequestProviderImpl implements CustomizationRequestProvider {

    @Resource
    private CustomizationRequestService customizationRequestService;

    @Override
    public DubboResponse<List<String>> copySkuAndSaveCustomizationRequest(CustomizationRequestReq req) {
        // 调用Service层的事务方法
        List<String> newSkus = customizationRequestService.copySkuAndSaveCustomizationRequestWithTransaction(req);
        return DubboResponse.getOK(newSkus);
    }

}
