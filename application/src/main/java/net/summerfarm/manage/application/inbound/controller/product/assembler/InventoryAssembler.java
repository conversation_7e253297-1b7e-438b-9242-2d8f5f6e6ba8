package net.summerfarm.manage.application.inbound.controller.product.assembler;

import net.summerfarm.manage.application.inbound.controller.product.input.command.SkuBaseInfoCommandInput;
import net.summerfarm.manage.domain.product.param.command.InventoryCommandParam;

/**
 * @Description
 * @Date 2025/4/15 16:58
 * @<AUTHOR>
 */
public class InventoryAssembler {

    public static InventoryCommandParam convert(SkuBaseInfoCommandInput input) {
        InventoryCommandParam commandParam = new InventoryCommandParam();
        commandParam.setSku(input.getSku());
        commandParam.setWeightNum(input.getWeightNum());
        commandParam.setVolume(input.getVolume());
        return commandParam;
    }

}
