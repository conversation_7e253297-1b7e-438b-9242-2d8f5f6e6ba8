package net.summerfarm.manage.application.inbound.controller.major.input.query;

import lombok.Data;
import java.io.Serializable;

import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-02-19 11:19:11
 * @version 1.0
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MajorPriceLogQueryInput extends BasePageInput implements Serializable{

	/**
	 * sku
	 */
	private String sku;

	/**
	 * areano
	 */
	private Integer areaNo;

	/**
	 * 1 账期 2  现结
	 */
	private Integer direct;

	/**
	 * 大客户id
	 */
	private Long adminId;


}