package net.summerfarm.manage.application.inbound.controller.price.vo;

import lombok.Data;
import net.summerfarm.manage.common.enums.price.StockWarningLabelEnum;

import java.math.BigDecimal;

@Data
public class DocInfoVO {

    /**
     * doc 指“Days of Coverage”，即库存覆盖天数
     * 空展示：未配置/0无法计算
     */
    private BigDecimal doc;

    /**
     * 商品分类，A,B,C,Z
     * @see net.xianmu.scp.common.enums.SkuWarehouseSellLabelEnums.LevelLabel
     */
    private String levelLabel;

    /**
     * 库存消耗标准天数下限
     * 空展示：0无法计算/未配置
     */
    private BigDecimal stockLevelMinimumDay;

    /**
     * 目标库存天数
     */
    private BigDecimal stockLevelTargetDay;

    /**
     * 库存消耗标准天数上限
     * 空展示：0无法计算/未配置
     */
    private BigDecimal stockLevelMaximumDay;

    /**
     * 日销量
     * 空展示：0无法计算/未配置
     */
    private Integer salesQuantity;

    /**
     * 期初库存
     * 空展示：今日未更新
     */
    private BigDecimal initStockQuantity;

    /**
     * 库存预警描述
     * @see StockWarningLabelEnum#getContent()
     */
    private String stockWarmingLabelContent;

    /**
     * 库存预警状态值
     * @see StockWarningLabelEnum#getValue()
     */
    private Integer stockWarmingLabelValue;

}
