package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.order.OrderCommandService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @ClassName AutoConfirmJob
 * @Description 自动收货
 * <AUTHOR>
 * @Date 17:23 2024/1/17
 * @Version 1.0
 **/
@Component
@Slf4j
public class AutoConfirmProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private OrderCommandService orderCommandService;

    private static final String ORDER_NO_KEY = "orderNo";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("订单自动确认收货 start :{}", LocalDateTime.now());
        String orderNo = null;
        if (StringUtils.isNotBlank(context.getInstanceParameters())) {
            JSONObject jsonObject = JSON.parseObject(context.getInstanceParameters());
            if (jsonObject.containsKey(ORDER_NO_KEY)) {
                orderNo = jsonObject.getString(ORDER_NO_KEY);
            }
        }
        orderCommandService.autoConfirm(orderNo);
        log.info("订单自动确认收货 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}
