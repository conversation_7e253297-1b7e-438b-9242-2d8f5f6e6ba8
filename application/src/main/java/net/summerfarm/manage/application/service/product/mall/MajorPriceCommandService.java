package net.summerfarm.manage.application.service.product.mall;


import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceInput;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceUpdateBatchInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.ExcelImportResDTO;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @date 2024-04-08 15:19:21
 * @version 1.0
 */
public interface MajorPriceCommandService {

    void newLowPriceRemainder(Integer adminId, Integer areaNo, String sku);


    /**
     * 批量修改 mallshow
     * @param mallShow
     * @param direct
     * @param adminId
     */
    void majorPriceMallShowBatchUpdate(Integer mallShow, Integer direct, Integer adminId);

    /**
     * 保存报价单
     * @param input
     */
    void addMajorPrice(List<MajorPriceInput> input);

    void commitBatch(List<Long> ids);

    /**
     * 批量改价
     * @param input
     */
    void updateBatch(MajorPriceUpdateBatchInput input);

    void fillPriceInfo(MajorPriceCommandParam param, ProductCostQueryResp productCostQueryResp);

}