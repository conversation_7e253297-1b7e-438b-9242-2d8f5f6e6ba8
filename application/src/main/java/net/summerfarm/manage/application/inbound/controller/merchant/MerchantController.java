package net.summerfarm.manage.application.inbound.controller.merchant;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.merchant.assembler.MerchantAssembler;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MajorMerchantQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCommandInput;

import net.summerfarm.manage.application.inbound.controller.merchant.vo.AccountChangeVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MajorCustomerMerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantSubAccountVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactAdjustVO;
import net.summerfarm.manage.application.service.account.AccountChangeService;
import net.summerfarm.manage.application.service.account.MerchantAccountService;
import net.summerfarm.manage.application.service.contact.ContactAdjustService;
import net.summerfarm.manage.application.service.merchant.MerchantCommandService;
import net.summerfarm.manage.application.service.merchant.MerchantQueryService;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/18 11:28
 */

@RestController
@RequestMapping("/merchant")
public class MerchantController {

    @Autowired
    private MerchantCommandService merchantCommandService;
    @Autowired
    private MerchantQueryService merchantQueryService;
    @Resource
    private AccountChangeService accountChangeService;
    @Resource
    private ContactAdjustService contactAdjustService;
    @Resource
    MerchantAccountService merchantAccountService;
    /**
     * 待审核/已注册/审核未通过/黑名单
     *
     * @param selectKeys
     * @return
     */
    @RequiresPermissions(value = {"merchant:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/page", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantVO>> getPage(@RequestBody MerchantQueryInput selectKeys) {
        return CommonResult.ok(merchantQueryService.getPage(selectKeys));
    }


    /**
     * 查询门店详情
     *
     * @param mId
     * @return
     */
    @RequiresPermissions(value = {"merchant:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/detail/{mId}", method = RequestMethod.POST)
    public CommonResult<MerchantVO> getDetail(@PathVariable Long mId) {
        return CommonResult.ok(merchantQueryService.getDetail(mId));
    }



    /**
     * 更换账号审核详情
     *
     * @param selectKeys
     * @return
     */
    @RequiresPermissions(value = {"merchant:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/account-audit/detail", method = RequestMethod.POST)
    public CommonResult<AccountChangeVO> changeDetail(@RequestBody MerchantQueryInput selectKeys) {
        return CommonResult.ok(accountChangeService.changeDetail(selectKeys));
    }


    /**
     * 更换审核列表
     *
     * @param selectKeys
     * @return
     */
    @RequiresPermissions(value = {"merchant:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/account-audit-page", method = RequestMethod.POST)
    public CommonResult<PageInfo<AccountChangeVO>> auditPage(@RequestBody MerchantQueryInput selectKeys) {
        return CommonResult.ok(accountChangeService.queryPage(selectKeys));
    }


    /**
     * 地址更换审核详情
     *
     * @param selectKeys
     * @return
     */
    @RequiresPermissions(value = {"merchant:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/adjust-contact/detail", method = RequestMethod.POST)
    public CommonResult<ContactAdjustVO> adjustDetail(@RequestBody MerchantQueryInput selectKeys) {
        return CommonResult.ok(contactAdjustService.getDetail(selectKeys));
    }

    /**
     * 地址更换审核列表
     *
     * @param selectKeys
     * @return
     */
    @RequiresPermissions(value = {"merchant:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/adjust-contact/page", method = RequestMethod.POST)
    public CommonResult<PageInfo<ContactAdjustVO>> adjustPage(@RequestBody MerchantQueryInput selectKeys) {
        return CommonResult.ok(contactAdjustService.getPage(selectKeys));
    }


    /**
     * 门店账号列表
     *
     * @param selectKeys
     * @return
     */
    @RequiresPermissions(value = {"merchant:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/account-list", method = RequestMethod.POST)
    public CommonResult<List<MerchantSubAccountVO>> merchantAccountList(@RequestBody MerchantQueryInput selectKeys) {
        return CommonResult.ok(merchantAccountService.getByMId(selectKeys.getMId()));
    }

    /**
     * 新增门店
     * @param merchant
     * @return
     */
    @RequestMapping(value = "/upsert/insert", method = RequestMethod.POST)
    public CommonResult<MerchantVO> addMerchant(MerchantCommandInput merchant) {
        return CommonResult.ok(MerchantAssembler.toMerchantVO(merchantCommandService.addMerchant(MerchantAssembler.toMerchantEntity(merchant))));
    }

    /**
     * 倒入茶百道门店
     * @param file
     * @return
     */
    @RequestMapping(value = "/upsert/batch-insert/cbd", method = RequestMethod.POST)
    public CommonResult<String> batchMerchantToCBD(@RequestParam("file") MultipartFile file) {
        return merchantCommandService.saveBatch(file, false);
    }

    /**
     * 大客户下门店账号列表
     *
     * @param selectKeys
     * @return
     */
    @RequiresPermissions(value = {"major:select"})
    @RequestMapping(value = "/query/major/merchant-list", method = RequestMethod.POST)
    public CommonResult<PageInfo<MajorCustomerMerchantVO>> majorMerchantList(@RequestBody MajorMerchantQueryInput selectKeys) {
        return CommonResult.ok(merchantQueryService.majorMerchantList(selectKeys));
    }
}
