package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import net.summerfarm.common.util.StringUtils;

import java.io.Serializable;
import java.util.List;
@Data
public class MerchantContactAddressRemarkVO  implements Serializable {
    private static final long serialVersionUID = 5640865689281001205L;
    /**
     * 自定义备注
     */
    private String customRemark;
    /**
     * 基础备注集合
     */
    private List<String> baseRemark;


    public String toJsonStr(MerchantContactAddressRemarkVO contactAddressRemark){
        return toJsonStr(contactAddressRemark);
    }

    public static  MerchantContactAddressRemarkVO initContactAddressRemark(String json){
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JSONUtil.toBean(json,MerchantContactAddressRemarkVO.class) ;
    }
}
