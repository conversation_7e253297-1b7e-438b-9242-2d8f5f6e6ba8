package net.summerfarm.manage.application.service.product.mall.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.product.input.AreaSkuQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.input.query.AreaSkuMinPriceQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.AreaSkuVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.SkuAreaWarehouseNoMapVO;
import net.summerfarm.manage.application.service.product.converter.AreaSkuConverter;
import net.summerfarm.manage.application.service.product.mall.AreaSkuQueryService;
import net.summerfarm.manage.common.enums.AreaStatusEnum;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.param.query.AreaSkuQueryParam;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.summerfarm.manage.facade.pms.PriceConfigQueryFacade;
import net.summerfarm.manage.facade.pms.dto.PriceConfigQueryDTO;
import net.summerfarm.manage.facade.pms.input.PriceConfigQueryInput;
import net.summerfarm.manage.facade.wnc.WarehouseSkuAreaNoQueryFacade;
import net.summerfarm.manage.facade.wnc.WncDeliveryFenceQueryFacade;
import net.summerfarm.manage.facade.wnc.dto.AreaWarehouseNoSkuDTO;
import net.summerfarm.manage.facade.wnc.input.SkuWarehouseNoQueryAreaInput;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AreaSkuQueryServiceImpl implements AreaSkuQueryService {
    @Autowired
    private AreaSkuQueryRepository areaSkuQueryRepository;

    @Resource
    private AreaQueryRepository areaQueryRepository;

    @Autowired
    private WarehouseSkuAreaNoQueryFacade warehouseSkuAreaNoQueryFacade;

    @Resource
    private WncDeliveryFenceQueryFacade wncDeliveryFenceQueryFacade;

    @Resource
    private PriceConfigQueryFacade priceConfigQueryFacade;

    @Override
    public List<AreaSkuEntity> queryAreaSkuBySkuList(List<String> skus, Boolean onsale) {
        if(CollectionUtil.isEmpty (skus)){
            return Collections.emptyList ();
        }
        return areaSkuQueryRepository.queryListSkuPrice (skus, Collections.emptyList (),onsale);
    }
    @Override
    public SkuAreaWarehouseNoMapVO queryAvailableAreaMap(Map<String, List<Integer>> skuLargeAreaNoMap,Collection<Integer> largeAreaNos) {
        SkuAreaWarehouseNoMapVO skuAreaWarehouseNoMapVO = new SkuAreaWarehouseNoMapVO ();
        Map<String, Set<Integer>> skuAreaNoMap = new HashMap<> ();
        Map<String, Integer> sku_AreaWarehouseNoMap = new HashMap<>();

        Set<String> skus = skuLargeAreaNoMap.keySet ();

        List<AreaSimpleEntity> areas = areaQueryRepository.batchQueryByLargeAreaNos (largeAreaNos);
        if(!CollectionUtils.isEmpty (areas)){
            areas = areas.stream ().filter (a -> AreaStatusEnum.OPEN.getValue ().equals (a.getAreaStatus ())).collect (Collectors.toList ());
        }
        if(CollectionUtils.isEmpty (areas)){
            log.info ("新增报价单返回，没有可用areano,inputs={}", JSON.toJSONString (skuLargeAreaNoMap));
            return skuAreaWarehouseNoMapVO;
        }
        Map<Integer, AreaSimpleEntity> areaMap = areas.stream().collect(Collectors.toMap(AreaSimpleEntity::getAreaNo,e->e));

        Map<Integer, Set<Integer>> largeAreaAreaNoMap = areas.stream().collect(Collectors.groupingBy(AreaSimpleEntity::getLargeAreaNo, Collectors.mapping(AreaSimpleEntity::getAreaNo, Collectors.toSet ())));
        skuAreaWarehouseNoMapVO.setLargeAreaAreaNoMap (largeAreaAreaNoMap);

        Map<Integer, List<AreaSimpleEntity>> largeAreaNoAreasMap = areas.stream().collect(Collectors.groupingBy(AreaSimpleEntity::getLargeAreaNo));
        List<Integer> areaNos = areas.stream ().map (AreaSimpleEntity::getAreaNo).collect (Collectors.toList ());

        //查询areasku
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.queryListSkuPrice (new ArrayList<> (skus), areaNos, null);
        if(CollectionUtils.isEmpty (areas)){
            log.info ("新增报价单返回，没有可用areasku,inputs={}",JSON.toJSONString (skuLargeAreaNoMap));
            return skuAreaWarehouseNoMapVO;
        }
        Map<String, List<AreaSkuEntity>> skuAreaSkuMap = areaSkuEntities.stream ().filter (areaSkuEntity -> areaSkuEntity.getPrice () != null).collect(Collectors.groupingBy(AreaSkuEntity::getSku));
        if(CollectionUtils.isEmpty (skuAreaSkuMap)){
            log.info ("新增报价单返回，没有可用areasku price,inputs={}",JSON.toJSONString (skuLargeAreaNoMap));
            return skuAreaWarehouseNoMapVO;
        }

        //用围栏状态过滤areano
        Map<String, Set<Integer>> skuAreaNosMap = new HashMap<> ();
        skuAreaSkuMap.forEach ((sku, areaSkuList)->{
            skuAreaNosMap.put (sku,areaSkuList.stream().map (AreaSkuEntity::getAreaNo).collect(Collectors.toSet ()));
        });
        Map<String, List<WarehouseBySkuAreaNoResp>> skuAreasMap = getWarehouseBySkuAreaNoRespMap(skuAreaNosMap);

        //取交集
        skuLargeAreaNoMap.forEach ((sku, largeAreaNoList)-> largeAreaNoList.forEach (largeAreaNo-> largeAreaNoAreasMap.get (largeAreaNo).forEach (area -> {
            if(skuAreaSkuMap.containsKey (sku) && skuAreasMap.containsKey (sku) &&
                    skuAreaSkuMap.get (sku).stream ().anyMatch (areaSkuEntity -> areaSkuEntity.getAreaNo ().equals (area.getAreaNo ()))
            ){
                List<WarehouseBySkuAreaNoResp> respsList = skuAreasMap.get (sku);
                WarehouseBySkuAreaNoResp resp = respsList.stream ().filter (r -> r.getAreaNo ().equals (area.getAreaNo ())).findFirst ().orElse (null);
                if(resp != null) {
                    skuAreaNoMap.putIfAbsent (sku, new HashSet<> ());
                    skuAreaNoMap.get (sku).add (area.getAreaNo ());
                    sku_AreaWarehouseNoMap.put (sku + "_" + area.getAreaNo (),resp.getWarehouseNo ());
                }
            }
        })));
        skuAreaWarehouseNoMapVO.setSkuAreaNoMap (skuAreaNoMap);
        skuAreaWarehouseNoMapVO.setSku_AreaWarehouseNoMap (sku_AreaWarehouseNoMap);
        skuAreaWarehouseNoMapVO.setAreaMap (areaMap);
        skuAreaWarehouseNoMapVO.setSkuAreaSkuMap (skuAreaSkuMap);
        return skuAreaWarehouseNoMapVO;
    }

    /**
     * req <sku,List<AreaNo>>
     * return <sku + "_" + areano,WarehouseNo>
     * @return
     */
    @Override
    public Map<String, List<WarehouseBySkuAreaNoResp>> getWarehouseBySkuAreaNoRespMap(Map<String, Set<Integer>> skuAreaNoMap) {
        Map<String, List<WarehouseBySkuAreaNoResp>> skuAreasMap = new HashMap<> ();
        //用围栏状态过滤areano
        WarehouseBySkuAreaNoQueryReq req = new WarehouseBySkuAreaNoQueryReq ();
        List<WarehouseBySkuAreaNoDataReq> areaSkuReqs = new ArrayList<> ();
        skuAreaNoMap.forEach ((sku, areaSkuList)->{
            WarehouseBySkuAreaNoDataReq reqData = new WarehouseBySkuAreaNoDataReq ();
            reqData.setSku (sku);
            reqData.setAreaNoList (new ArrayList<> (areaSkuList));
            areaSkuReqs.add (reqData);
        });
        req.setAreaSkuList (areaSkuReqs);
        List<WarehouseBySkuAreaNoResp> resps = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo (req);
        if(CollectionUtils.isEmpty (resps)){
            log.info ("没有可用围栏,inputs={}",JSON.toJSONString (skuAreaNoMap));
            return skuAreasMap;
        }
        return resps.stream().collect(Collectors.groupingBy(WarehouseBySkuAreaNoResp::getSku));
    }

    @Override
    public PageInfo<Integer> pageOnsaleIdsByAreaNo(Set<Integer> areaNos,Integer pageIndex,Integer pageSize) {
        return areaSkuQueryRepository.pageOnsaleIdsByAreaNo(areaNos, pageIndex, pageSize);
    }

    @Override
    public List<AreaSkuEntity> queryAreaSkuBySkuAndAreaNoList(List<AreaSkuQueryInput> input) {
        List<AreaSkuQueryParam> param = AreaSkuConverter.toAreaSkuQueryParams(input);
        return areaSkuQueryRepository.queryAreaSkuBySkuAndAreaNoList(param);
    }

    @Override
    public List<AreaSkuVO> queryMinPrice(List<AreaSkuMinPriceQueryInput> inputs) {
        //根据 sku+库存仓查运营区域
        List<SkuWarehouseNoQueryAreaInput> skuWarehouseNoQueryAreaInputs = new ArrayList<>();
        inputs.forEach(input->{
            SkuWarehouseNoQueryAreaInput skuWarehouseNoQueryAreaInput = new SkuWarehouseNoQueryAreaInput();
            skuWarehouseNoQueryAreaInput.setSku(input.getSku());
            skuWarehouseNoQueryAreaInput.setWarehouseNo(input.getWarehouseNo());
            skuWarehouseNoQueryAreaInputs.add(skuWarehouseNoQueryAreaInput);
        });
        List<AreaWarehouseNoSkuDTO> areaWarehouseNoSkuDTOS = wncDeliveryFenceQueryFacade.queryAreaByListWarehouseAndSku(skuWarehouseNoQueryAreaInputs);
        if (CollectionUtils.isEmpty(areaWarehouseNoSkuDTOS)) {
            log.info("查询最小价格失败，没有可用区域,skuWarehouseNoQueryAreaInputs={}", JSON.toJSONString(skuWarehouseNoQueryAreaInputs));
            return Collections.emptyList();
        }

        List<AreaSkuQueryParam> param = new ArrayList<>();
        areaWarehouseNoSkuDTOS.forEach(areaWarehouseNoSkuDTO -> {
            if (CollectionUtils.isEmpty(areaWarehouseNoSkuDTO.getAreaNos())) {
                return;
            }
            AreaSkuQueryParam areaSkuQueryParam = new AreaSkuQueryParam();
            areaSkuQueryParam.setSku(areaWarehouseNoSkuDTO.getSku());
            areaSkuQueryParam.setAreaNos(areaWarehouseNoSkuDTO.getAreaNos());
            param.add(areaSkuQueryParam);
        });
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.queryAreaSkuBySkuAndAreaNoList(param);
        if (CollectionUtils.isEmpty(areaSkuEntities)) {
            log.info("查询最小价格失败，没有可用areasku,param={}", JSON.toJSONString(param));
            return Collections.emptyList();
        }

        //按照sku分组 然后获取最低价格
        Map<String, Optional<AreaSkuEntity>> minPriceBySku = areaSkuEntities.stream().collect(Collectors.groupingBy(AreaSkuEntity::getSku,
                Collectors.minBy(Comparator.comparingDouble((AreaSkuEntity e) -> e.getPrice().doubleValue()))));
        List<AreaSkuVO> result = minPriceBySku.values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(AreaSkuConverter::entityToVOV2)
                .collect(Collectors.toList());

        //获取佣金比例
        List<String> skus = inputs.stream().map(AreaSkuMinPriceQueryInput::getSku).collect(Collectors.toList());
        Long supplierId = inputs.get(0).getSupplierId();
        Integer warehouseNo = inputs.get(0).getWarehouseNo();
        PriceConfigQueryInput configQueryInput = new PriceConfigQueryInput();
        configQueryInput.setSupplierId(supplierId);
        configQueryInput.setWarehouseNo(warehouseNo);
        configQueryInput.setSkuList(skus);
        Map<String, List<PriceConfigQueryDTO>> priceConfigQueryDTOMap = priceConfigQueryFacade.queryPriceConfig(configQueryInput);
        if (CollectionUtils.isEmpty(priceConfigQueryDTOMap)) {
            return result;
        }
        result.forEach(areaSkuVO -> {
            List<PriceConfigQueryDTO> priceConfigQueryDTOS = priceConfigQueryDTOMap.get(areaSkuVO.getSku());
            if (CollectionUtils.isEmpty(priceConfigQueryDTOS)) {
                return;
            }
            PriceConfigQueryDTO selectedDTO = priceConfigQueryDTOS.stream()
                    .filter(dto -> {
                        if (dto.getWarehouseNo() != null && dto.getWarehouseNo().equals(warehouseNo)) {
                            return true;
                        } else if (dto.getWarehouseNo() == null) {
                            // 可以选择保留没有 warehouseNo 的对象作为备选
                            return true;
                        }
                        return false;
                    })
                    // 如果有多个匹配项，取第一个；根据业务需要可替换为更复杂的逻辑
                    .findFirst()
                    .orElse(null);
            if (selectedDTO != null) {
                areaSkuVO.setStepPriceList(selectedDTO.getStepPriceList());
            }
        });
        return result;
    }
}
