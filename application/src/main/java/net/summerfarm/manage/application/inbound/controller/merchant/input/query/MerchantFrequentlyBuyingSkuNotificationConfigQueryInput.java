package net.summerfarm.manage.application.inbound.controller.merchant.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Data
public class MerchantFrequentlyBuyingSkuNotificationConfigQueryInput extends BasePageInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 门店id
	 */
	private Long mId;

	/**
	 * 特价提醒 0：不提醒  1：提醒（默认）
	 */
	private Integer specialOffer;

	/**
	 * 到货提醒 0：不提醒  1：提醒（默认）
	 */
	private Integer goodsArrived;



}