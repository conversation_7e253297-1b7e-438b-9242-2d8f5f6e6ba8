package net.summerfarm.manage.application.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.xianmu.authentication.common.utils.SpringContextUtil;
import net.xianmu.common.exception.BizException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
public class PoiSearchUtil {
    
    private static final String KEYWORD_SEARCH_API = "https://restapi.amap.com/v3/place/text";
    private static final CloseableHttpClient httpClient;
    
    static {
        // 配置连接池
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100);
        connectionManager.setDefaultMaxPerRoute(20);
        
        // 配置请求参数
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000)
                .setSocketTimeout(10000)
                .setConnectionRequestTimeout(3000)
                .build();
        
        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }
    
    /**
     * 根据地址查询POI信息
     * @param address 地址信息
     * @return POI坐标信息，格式为"经度,纬度"
     */
    public static String getPoiByAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return "";
        }
        
        address = address.replace(" ", "");
        
        try {
            NacosPropertiesHolder nacosPropertiesHolder = SpringContextUtil.getBean("nacosPropertiesHolder", NacosPropertiesHolder.class);
            String encodedAddress = URLEncoder.encode(address, StandardCharsets.UTF_8.toString());
            String url = String.format("%s?keywords=%s&key=%s", 
                    KEYWORD_SEARCH_API, encodedAddress, API_KEY);
            
            HttpGet httpGet = new HttpGet(url);
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                String result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = JSON.parseObject(result);
                
                if (!"1".equals(jsonObject.getString("status"))) {
                    log.error("高德API请求失败: {}", jsonObject.getString("info"));
                    throw new BizException("高德API请求失败: " + jsonObject.getString("info"));
                }
                
                JSONArray poiArray = jsonObject.getJSONArray("pois");
                if (poiArray == null || poiArray.isEmpty()) {
                    return "";
                }
                
                JSONObject poi = poiArray.getJSONObject(0);
                return poi.getString("location");
                
            }
        } catch (IOException e) {
            log.error("HTTP请求异常", e);
            throw new BizException("网络请求失败");
        } catch (Exception e) {
            log.error("查询POI异常", e);
            throw new BizException("查询POI失败");
        }
    }
    
    /**
     * 关闭HTTP客户端连接池（应用关闭时调用）
     */
    public static void shutdown() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (IOException e) {
            log.error("关闭HTTP客户端失败", e);
        }
    }
}