package net.summerfarm.manage.common.enums.products;

import lombok.Data;
import net.xianmu.common.enums.base.Enum2Args;

import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/8/15 15:51
 */

public enum SkuQuoteTypeEnum {

    DEFAULT(0, "默认类型"),
    JIN(1, "按斤报价"),
    NUMS(2, "按件报价");

    private final Integer type;
    private final String desc;

    private SkuQuoteTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
