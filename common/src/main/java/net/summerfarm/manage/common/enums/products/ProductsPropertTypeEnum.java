package net.summerfarm.manage.common.enums.products;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品属性type枚举
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2022-05-06
 */
@AllArgsConstructor
@Getter
public enum ProductsPropertTypeEnum {

    /**
     * 关键属性
     */
    CORE_PROPERTY(0, "关键属性"),

    /**
     * 销售属性
     */
    SALE_PROPERTY(1, "销售属性")

    ;

    private final int type;

    private final String description;



}
