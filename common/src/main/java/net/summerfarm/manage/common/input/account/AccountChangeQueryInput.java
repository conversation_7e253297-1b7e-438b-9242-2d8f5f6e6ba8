package net.summerfarm.manage.common.input.account;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023-10-26 16:20:19
 * @version 1.0
 *
 */
@Data
public class AccountChangeQueryInput extends BasePageInput implements Serializable{
	/**
	 * 
	 */
	private Long id;

	/**
	 * 
	 */
	private Long mId;

	/**
	 * 
	 */
	private Long accountId;

	/**
	 * 
	 */
	private String oldPhone;

	/**
	 * 
	 */
	private String oldContact;

	/**
	 * 
	 */
	private String newPhone;

	/**
	 * 
	 */
	private String newContact;

	/**
	 * 
	 */
	private String mname;

	/**
	 * 
	 */
	private String remark;

	/**
	 * 1待审核 2审核通过 3审核失败
	 */
	private Integer status;

	/**
	 * 
	 */
	private String openid;

	/**
	 * 
	 */
	private String unionid;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;



}