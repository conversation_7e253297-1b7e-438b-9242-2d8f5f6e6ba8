package net.summerfarm.manage.common.constants;

import com.google.common.base.Splitter;
import net.summerfarm.common.delayqueue.DelayQueueItem;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.PropertiesUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.RegConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.DelayQueue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Package: net.summerfarm.contexts
 * @Description: 系统常用参数
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
public class Global {
    public static final String CHECK_BILL_ROBOT_URL = "checkBillRobotUrl";
    public static final long ADD_TIME = 30;
    public static final Integer BIG_ROLEID = 14;
    private static final Logger logger = LoggerFactory.getLogger(Global.class);

    /**
     * 鲜沐租户id
     */
    public static final Long XM_TENANTID = 1L;
    /**
     * 超级管理员
     */
    public static final String SA = "SUPER_ADMIN";

    /**
     * 系统账号adminId
     */
    public static final int SYSTEM_ADMIN_ID = 1515;

    /**
     * 喜茶大客户账号adminId
     */
    public static final int HEYTEA_ADMIN_ID = 1507;

    /**
     * 通用Flag标记TRUE
     */
    public static Integer TRUE_FLAG = Integer.valueOf(1);

    /**
     * 通用Flag字段FALSE
     */
    public static Integer FALSE_FLAG = Integer.valueOf(0);

    /**
     *大客户提前截单
     */
    public static Integer CLOSE_ORDER_TYPE = 1;

    /**
     *大客户
     */
    public static String BIG_MERCHANT = "大客户";

    /**
     *大客户
     */
    public static String SINGLE_STORE = "单店";

    /**
     * 区域主管
     */
    public static String AREA_MANAGER = "区域主管";

    /**
     * 采购主管
     */
    public static String PURCHASE_MANAGE = "采购主管";

    /**
     * 加单时长30分钟
     */
    public static Integer PRE_TIME = 30;

    /**
     * 分隔符号
     */
    public static final String SEPARATING_SYMBOL = ",";

    /**
     * 分隔符号
     */
    public static final String SEPARATING_SYMBOL_SEMICOLON = ";";

    /**
     * 省略符号
     */
    public static final String ELLIPSIS_SYMBOL_SEMICOLON = "...";

    public static final String CODE_SEPARATING_SYMBOL = "S";
    /**
     * 分隔符号
     */
    public static final String CROSS_BAR = "-";

    /**
     * 分隔符号
     */
    public static final String CROSS_STR = "--";

    /**
     * 分隔符号
     */
    public static final String SCRIBE = "/";

    /**
    * 查询线索池数据上限
    */
    public static final Integer QUERY_CLUE_POOL_SIZE = 10;

    /**
     * 定时任务由商城执行
     */
    public static final String MALL_SERVICE = "mall";

    /**
     * 默认分隔器
     */
    public static final Splitter DEFAULT_SPLITTER = Splitter.on(SEPARATING_SYMBOL);

    /**
     * 延时队列
     */
    public static final BlockingQueue<DelayQueueItem> delayQueue = new DelayQueue<>();

    /**
     * 售后优惠券固定前缀
     */
    public static final String AFTER_SALE_COUPON_FRONT = "after_sale";

    /**
     * threadLocal
     */
    public static final ThreadLocal<Map<String,Object>> threadLocal = new ThreadLocal<>();

    /**
     * 城市编号、名称映射
     */
    public static final Map<Integer, String> areaMap = new HashMap<>();

    /**
     * 配送编号、名称映射
     */
    public static final Map<Integer, String> storeMap = new HashMap<>();

    /**
     * 仓库编号、名称映射
     */
    public static final Map<Integer, String> warehouseMap = new HashMap<>();

    /**
     * 出入库类型
     */
    public static final Set<Integer> storeRecordTypeSet = new HashSet<>();


    //优惠券参数
    /**
     * 审核通过
     */
    public static final String AFTER_REVIEW = "afterReview";

    /**
     * 出入库、调拨短信模板id
     */
    public static final String alicode="SMS_152288197";

    /**
    * 发送验证码模版id
    */
    public static final String ALI_CODE = "SMS_148770058";
    /**
     * 系统名称
     */
    public static final String SYSTEM_NAME = "系统";
    /**
     * 系统adminId
     */
    public static final Integer SYSTEM_ID = 0;

    /**
     * 每日截单时间-1 <= orderTIme < 每日截单时间
     */
    public static final LocalTime CLOSING_ORDER_TIME = LocalTime.of(22, 00, 00);

    /**
     * 南京仓
     * 每日截单时间-1 <= orderTIme < 每日截单时间
     */
    public static final LocalTime NJ_CLOSING_ORDER_TIME = LocalTime.of(22, 00, 00);

    public static final LocalTime TWENTY_THREE_CLOSING_TIME = LocalTime.of(23, 00, 00);

    public static final LocalTime NATURE_DAY_END_TIME = LocalTime.of(23,59,59);

    /**
     * 自然日开始时间
     */
    public static final LocalTime NATURE_DAY_START_TIME = LocalTime.of(00,00,00);

    /**
     * 每日截单时间-1 <= orderTIme < 每日截单时间
     */
    public static final LocalTime NAN_JING_CLOSING_ORDER_TIME = LocalTime.of(22, 00, 00);


    public static final LocalTime  TWENTY_THREE_CLOSING_ORDER_TIME =  LocalTime.of(23, 00, 00);

    /**
     * 大客户截单
     */
    public static final LocalTime BIG_CLOSING_ORDER_TIME = LocalTime.of(20, 00, 00);

    /**
     * 大客户截单
     */
    public static final LocalTime CBD_BIG_CLOSING_ORDER_TIME = LocalTime.of(18, 00, 00);

    /**
     * 春节配送时间
     */
    public static final LocalDate SPRING_FESTIVAL_DATE = LocalDate.of(2021, 02, 15);


    /**
     * 普通订单业务编号
     */
    public static final String NORMAL_ORDER_CODE = "01";

    /**
     * 团购订单业务编号
     */
    public static final String TIMING_ORDER_CODE = "02";

    /**
     * 补运费
     */
    public static final String DELIVERY_FEE_ORDER_CODE = "03";

    /**
     * 代下单
     */
    public static final String SUBSTITUTED_TAKE_ORDER_CODE = "04";

    /**
     * 喜茶代下单订单号
     */
    public static final String HT_SUBSTITUTED_TAKE_ORDER_CODE = "ht";

    /**
     * 预售订单编号
     */
    public static final String PRE_SALE_ORDER_CODE = "05";

    /**
     * 直发采购订单
     */
    public static final String DIRECT_PURCHASE_ORDER_CODE = "11";

    /**
     * 赠品数量限制
     */
    public static final String GIFT_AMOUNT_LIMIT = "gift_amount_limit";

    /**
     * 茶百道大客户adminId
     */
    public static final String CBD_ADMIN_ID = "CBD_admin_id";

    /**
     * SKU-SPU批量新增模板
     */
    public static final String SPU_SKU_INSERT_TEMPLATE = "SPU_SKU_Insert_Template";

    /**
     * SKU-SPU批量操作填写指南
     */
    public static final String COMPLETION_GUIDE = "COMPLETION_GUIDE";

    /**
     * 批量更新城市售卖信息模板
     */
    public static final String CITY_SALES_INFORMATION_TEMPLATE = "CITY_SALES_INFORMATION_TEMPLATE";

    /**
     * SKU-SPU批量修改模板
     */
    public static final String SPU_SKU_UPDATE_TEMPLATE = "SPU_SKU_Update_Template";

    /**
     * 100
     */
    public static final Integer ONE_HUNDRED = 100;

    /**
     * 50
     */
    public static final Integer FIFTY = 50;


    /**
     * saas截单
     */
    public static final LocalTime SAAS_ORDER_TIME = LocalTime.of(20, 0, 0);

    /**
     * 是否在更新订单销售出库状态
     */
    public static boolean IS_UPDATE_ORDER = false;

    public static final String APP_KEY = "dingotmyqkkux5rnj2u7";

    public static final String APP_SECRET = "N_uOnSK9L5SOumPkNN53IRl_oquP3UmpzDdNSHa1koyH6d225YHnPXkqnP55lNtc";

    public static final String CORPID ="ding0cf3c7a13521ce22ffe93478753d9884";

    public static final String EN_AES_KEY = "qwertyuiopasdfghjklzxcvbnm1234567890ASDFGHJ";

    public static final String AGENT_ID = "429869282";

    public static final String TOKEN = "xianmu619";

    public static final Integer GOODS_AREA = 10;

    /**
    * 省心送采购调拨计算
    */
    public static final Integer TI_MING_PURCHASE = 7;

    /**
    * 省心送调拨计算
    */
    public static final Integer TI_MING_ALLOCATION = 15;



    /**
    * 安佳在线sku
    */
    public static final String AJ_SKU = "56217";

    /**
    * 深圳 ：公司地址
    */
    public static final String SZ_NAME = "鲜沐农场华南分公司地址:";

    public static final String SZ_ADDRESS = "深圳市龙岗区平湖街道富安大道8号海源创新中心409";

    public static final String XM_NAME = "鲜沐农场公司地址:";

    public static final String XM_ADDRESS = "浙江省杭州市西湖区新杭商务中心1号楼501";

    /**
    * 备用单金额审批 钉钉 processCode
    */
    public static final String IMPREST_CODE = "PROC-65CD1B62-F8E8-4849-AE0E-8479E507DDB6";
    public static final String STORE_QUANTITY_CODE = "PROC-DA6BEE7E-8A4F-48DB-AB8F-F63746AD2213";
    public static final String PURCHASE_BACK_CODE = "PROC-3AEE5603-DB5B-4A61-B2C9-678D0F50628C";
    public static final String COST_CHANGE_CODE = "PROC-3D504A47-C9F5-483A-9F56-3E5F66BE4F3E";

    /**
     * 应收账款审批审核 钉钉 processCode
     */
    public static final String ACCOUNT_RECEIVABLE = "PROC-E845270B-9E5C-4FCD-AE3D-D83011DAC377";

    /**
     * ROOT目录
     */
    public static final String ROOT_PATH = System.getProperty("user.dir").substring(0, System.getProperty("user.dir").indexOf(File.separator));
    /**
     * excel模板文件目录
     */
    public static final String TEMPLATE_DIR = ROOT_PATH + File.separator + "data" + File.separator + "template";
    /**
     * excel报告文件目录
     */
    public static final String REPORT_DIR = ROOT_PATH + File.separator + "data" + File.separator + "import_report";
    /**
     * pdf文件目录
     */
    public static final String PDF_DIR = ROOT_PATH + File.separator + "data" + File.separator + "pdf";

    /**
    * 使用配送仓类型
    */
    public static final String TYPES =  "52,51,57";

    public static final String AREA_TYPES =  "50,53,57";


    /**
    * 修改配送路线信息key
    */
    public static final String DELIVERY_PATH_UPDATE_KEY = "delivery_path_update";

    public static final String REDIS_DELIMITER = "_";



    /**
     * 当前时间的截单开始时间
     * 若当前时间>=截单时间，return: {当前日期 }
     * 当前时间<截单时间，return：{(当前日期-1) }
     *
     * @return
     */
    public static LocalDateTime getStartTime() {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), CLOSING_ORDER_TIME);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    public static LocalDateTime getSampleStartTime() {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), BIG_CLOSING_ORDER_TIME);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    /**
     * 当前时间的截单开始时间
     * 若当前时间>=截单时间，return: {当前日期}
     * 当前时间<截单时间，return：{(当前日期-1)}
     *
     * @return
     */
    public static LocalDateTime getStartTime(LocalTime closeTime) {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), closeTime);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    /**
     * 当前时间本月的 截单开始时间
     *
     * @return
     */
    public static LocalDateTime getMonthStartTime() {
        // 取当前日期：
        LocalDate today = LocalDate.now();
        LocalTime time = LocalTime.now();
        LocalDate lastDayOfThisMonth = today.with(TemporalAdjusters.lastDayOfMonth());
        if (lastDayOfThisMonth.isEqual(today) && time.isAfter(CLOSING_ORDER_TIME)) {
            return LocalDateTime.of(today, CLOSING_ORDER_TIME);
        } else {
            return LocalDateTime.of(today.with(TemporalAdjusters.firstDayOfMonth()).minusDays(1), CLOSING_ORDER_TIME);
        }
    }

    /**
     * 当前时间 月份开始时间
     *
     * @return
     */
    public static LocalDateTime getMonthStartTime(LocalDateTime now) {
        LocalDateTime start = null;
        LocalDate firstday = LocalDate.of(now.toLocalDate().getYear(), now.toLocalDate().getMonth(), 1);
        LocalDate lastDay = now.toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
        if (now.toLocalDate().isEqual(lastDay) && now.toLocalTime().isAfter(CLOSING_ORDER_TIME)) {
            start = lastDay.atTime(CLOSING_ORDER_TIME);
        } else {
            start = firstday.minusDays(1).atTime(CLOSING_ORDER_TIME);
        }
        return start;
    }

    /**
     * 得到本月的结束时间
     * @return
     */
    public static LocalDateTime getMonthEndTime(){
        LocalDateTime startTime = getMonthStartTime();
        return startTime.plusDays(1).with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 指定时间的截单开始时间
     *
     * @return
     */
    public static LocalDateTime getStartTimeByDate(LocalDateTime localDateTime) {
        LocalDate localDate =localDateTime.toLocalDate();
        LocalDateTime closingTime = LocalDateTime.of(localDate, CLOSING_ORDER_TIME);
        return localDateTime.isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    /**
     * 生成订单号
     *
     * @param orderCode
     * @return
     */
    public static String createOrderNo(String orderCode) {
        return orderCode + System.currentTimeMillis() + StringUtils.orderRandomNum();
    }

    /**
     * 收集异常堆栈信息
     */
    public static String collectExceptionStackMsg(Exception e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw, true));
        String strs = sw.toString();
        return strs;
    }

    public static BigDecimal getVolume(String volume){
        if (!volume.matches(RegConstant.VOLUME_REG)){
            return BigDecimal.ZERO;
        }
        String[] vs = volume.split("\\*");
        BigDecimal v = BigDecimal.valueOf(1);
        for (String s: vs){
            v = v.multiply(BigDecimal.valueOf(Double.valueOf(s)));
        }
        return v;
    }





    /**
     * 当前时间的截单开始时间
     * 若当前时间>=截单时间，return: {当前日期 }
     * 当前时间<截单时间，return：{(当前日期-1) }
     *
     * @return
     */
    public static LocalDateTime getBigStartTime() {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), BIG_CLOSING_ORDER_TIME);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    /**
     * 指定时间的截单开始时间
     * 若指定时间>=截单时间，return: {指定日期 }
     * 指定时间<截单时间，return：{(指定日期-1) }
     *
     * @return
     */
    public static LocalDateTime getAppointTime(LocalDateTime appointTime) {
        LocalDateTime time = BaseDateUtils.getAppointTime(appointTime);
        return appointTime.isBefore(time) ? BaseDateUtils.getDayStart(appointTime) : BaseDateUtils.getDayStart(appointTime.plusDays(1));
    }




    public  static String getErrorInfoFromException(Exception e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            pw.flush();
            sw.flush();
            return "\r\n" + sw + "\r\n";
        } catch (Exception e2) {
            return "ErrorInfoFromException";
        }
    }

    /**
    * 获取code
    */

    public static String getBatchCode(String onlyCode){
        if(StringUtils.isEmpty(onlyCode)){
            return "";
        }
        StringBuffer codeBuff = new StringBuffer();
        String code = onlyCode.split(Global.CODE_SEPARATING_SYMBOL)[0];
        codeBuff.append(code)
                .append(Global.CODE_SEPARATING_SYMBOL);

        return codeBuff.toString();

    }

    /**
     * 去除weight字段中0||1_ 是否展示平均价部分
     * @param weight
     * @return
     */
    public static String subWeight(String weight){
        if (weight == null || "".equals(weight)){
            return weight;
        }
        String weight2 = weight;
        String specialStr = "";
        if (weight2.contains("(")){ //去除规格说明部分 (特殊说明)
            weight2 = weight2.substring(0,weight2.indexOf("("));
            specialStr = weight.substring(weight.indexOf("("),weight.length());
        }
        if (weight2.matches(RegConstant.WEIGHT_REG1)){
            if (weight2.contains("_")){ //去除是否展示平均价部分 0||1_ 0代表不展示，1代表展示
                weight2 = weight2.substring(2,weight2.length());
            }
            BigDecimal value1 = BigDecimal.valueOf(1);
            BigDecimal value2 = BigDecimal.valueOf(1);
            Matcher matcher = Pattern.compile(Global.SUB_WEIGHT).matcher(weight2);
            if (matcher.find()){
                value1 = BigDecimal.valueOf(Double.parseDouble(matcher.group()));
            }
            if (matcher.find()){
                value2 = BigDecimal.valueOf(Double.parseDouble(matcher.group()));
            }
            if (value2.compareTo(BigDecimal.valueOf(1)) <= 0){
                return weight.substring(weight.indexOf("_")+1,weight.length()).split("\\*")[0]+specialStr;
            }
        }
        if (StringUtils.isNotBlank(weight) && weight.contains("_")){
            return weight.substring(weight.indexOf("_")+1,weight.length());
        }
        return weight;
    }

    /**
     * 生成pdNo
     * @param categoryId 类目id
     * @return
     */
    public static String generatePdNo(Integer categoryId) {
        return categoryId + "" + StringUtils.getRandomNumber(5);
    }

    public static final String AES_KEY="M2I4ZWE2NmQ3YzJhNDIxYzk0M2NiNjE0NDgwZmVlZDY";

    public static final String EVENT_TOKEN="3qVbfBLUmQ6wB0un9OBG16UGdcySC7Z3";

    public static final String EVENT_CORPID="dingotmyqkkux5rnj2u7";

    public static final String AKID="LTAI5tFD1hqAp92tLSE8bhSo";

    public static final String AKSECRET="******************************";



    /**
     * 商城域名
     */
    public static String DOMAIN_NAME = "https://h5.summerfarm.net";

    /**
     * 顶级域名
     */
    public static String TOP_DOMAIN_NAME;
    /**
     * 商城审核
     */
    public static String HANDLE_URL;
    /**
     * 获取已到货/未到货售后
     */
    public static String DELIVERY_STATUS;
    /**
     * 获取售后服务类型
     */
    public static String GET_HANDLE_TYPE;
    /**
     * 最大售后数量
     */
    public static String MAX_QUANTITY;
    /**
     * 最大售后金额
     */
    public static String MAX_MONEY;
    /**
     * 批量生成售后单
     */
    public static String AFTER_BATCH_SAVE;
    /**
     * 商城审批
     */
    public static String AUDIT_URL;
    /**
     * 提交售后
     */
    public static String SAVE_URL;
    /**
     * 用户标签
     */
    public static String SIGN_TAGS_URL;
    /**
     * 模拟下单接口
     */
    public static String WX_SIMULATION_JSAPI_PAY;
    /**
     * 首页商品列表
     */
    public static String HOME_PRODUCT;
    /**
     * 代下单
     */
    public static String HELP_ORDER;
    /**
     * 售后券
     */
    public static String AfterSaleCoupon;
    /**
     * AOL下单
     */
    public static String AOL_HELP_ORDER;
    /**
     * 微信转账地址
     */
    public static String WX_TRANSFER_URL;
    /**
     * 提交售后
     */
    public static String SAAS_QUERY_AFTER_SALE;

    /**
     * 应收账款审批上传的图片前缀
     */
    public static String RECEIVABLE_PICTURE_PATH_PREFIX;
    /**
     * 限制备注汉字字符
     */
    public static Integer LIMIT_QUANTITY =300;

    /**
     * saas售后信息查询
     */
    public static String SAAS_DOMAIN_NAME;

    /**
     * 提交售后
     */
    public static String SAAS_QUERY_ORDER_SALE;

    /**
     * 查询Saas订单信息
     */
    public static String SAAS_ORDER_INFO;

    /**
     * Saas k8s域名
     */
    public static String SAAS_HOST;

    /**
     * 初始化api
     */
    public static void apiInit() {
        DOMAIN_NAME = PropertiesUtils.getProperty("xianmu.mall.domain");
        SAAS_DOMAIN_NAME = PropertiesUtils.getProperty("saas.domain");
        SAAS_HOST = PropertiesUtils.getProperty("saas.host");

        TOP_DOMAIN_NAME = "summerfarm.net";

        HANDLE_URL = DOMAIN_NAME + "/after-sale/handle";

        DELIVERY_STATUS = DOMAIN_NAME + "/after-sale/deliveryStatus";

        GET_HANDLE_TYPE = DOMAIN_NAME + "/after-sale/getHandleType";

        MAX_MONEY = DOMAIN_NAME + "/after-sale/manageAfterSaleMoney";

        MAX_QUANTITY = DOMAIN_NAME + "/after-sale/manageCalculateQuantity";

        AFTER_BATCH_SAVE = DOMAIN_NAME + "/after-sale/batchSave";

        AUDIT_URL = DOMAIN_NAME + "/after-sale/audit";

        SAVE_URL = DOMAIN_NAME + "/after-sale/manageSave";

        SIGN_TAGS_URL = DOMAIN_NAME + "/tags-manage";

        WX_SIMULATION_JSAPI_PAY = DOMAIN_NAME + "/wechatpay/simulation/JsapiPay";

        HOME_PRODUCT = DOMAIN_NAME + "/product/1/50";

        HELP_ORDER = DOMAIN_NAME + "/order/place-order/v2";

        AfterSaleCoupon = DOMAIN_NAME + "/after-sale/coupon";

        AOL_HELP_ORDER = DOMAIN_NAME + "/order/placeOrderToAOL";

        WX_TRANSFER_URL = DOMAIN_NAME + "/cash/transfer";

        RECEIVABLE_PICTURE_PATH_PREFIX = "https://cdn." + Global.TOP_DOMAIN_NAME + "/";

        SAAS_QUERY_AFTER_SALE = SAAS_DOMAIN_NAME + "/order/query";

        SAAS_QUERY_ORDER_SALE = SAAS_DOMAIN_NAME +"/order/after/sale/queryAllAfterSaleOrder";

        SAAS_ORDER_INFO = SAAS_HOST + "/order/get/orderInfo";
    }

    /**
     * 冻结库存标识
     */
    public static String LOCK = "lock";

    /**
     * 巡检执行标识
     */
    public static final String OVERSELL_INSPECTION = "lock:LockId:oversell_inspection";

    /**
     * 盘点项
     */
    public static final String LOSS = "盘亏";

    public static final String SURPLUS = "盘盈";

    public static final String DAMAGE = "货损";

    public static final String ALLOCATION_DAMAGE = "调拨货损";

    // 采购预售config配置key
    public static String ADVANCE_AREANO= "ADVANCE_AREANO";

    // 采购单备货期时长
    public static String STOCK_PERIOD= "stock_period";

    /**
     * 钉钉审批流url
     */
    public static final String PROCESS_INSTANCE_URL = "https://oapi.dingtalk.com/topapi/processinstance/create";
    /**
     * 获取钉钉审批流详情url
     */
    public static final String PROCESS_INSTANCE_DETAIL = "https://oapi.dingtalk.com/topapi/processinstance/get";
    /**
     * 钉钉客户倒闭审批模板code
     */
    public static final String CUSTOMER_FAIL_CODE = "PROC-68E164E8-A9FE-4701-8257-3359F19BAE34";
    /**
     * 卡券发放审批模板
     */
    public static final String COUPON_AUDIT_CODE = "PROC-0EA5F01A-8F1C-427C-84D2-63D0E29AA043";
    /**
     * DMS账号申请模板
     */
    public static final String DMS_ACCOUNT_AUDIT_CODE = "PROC-836EFD56-B7AE-49EC-AF09-9E53CF026C16";
    /**
     * 货损审批模板code
     */
    public static final String STOCKDAMAGE_AUDUT_CODE = "PROC-7A9933AB-33FE-4D20-8DB0-5A1AE8BF7A38";
    /**
     * 盘盈&盘亏模板code
     */
    public static final String STOCKTAKING_AUDUT_CODE = "PROC-68D9F311-5487-49BA-BF34-AC2D87E655E6";
    /**
     * 营销审批
     */
    public static final String MARKETING_AUDIT_CODE_DEV = "PROC-********-B678-41E5-BF30-438046CB51DB";
    public static final String MARKETING_AUDIT_CODE_PRO = "PROC-2CCE3428-49D5-48E8-B15A-E7472502FB67";
    /**
     * 七牛云成功标识
     */
    public static final String SUCCESS_FLAG = "SUCCESS";
    /**
     * 下载中心待上传
     */
    public static final Integer WAIT_UPLOAD = 0;
    /**
     * 下载中心上传成功
     */
    public static final Integer SUCCESS = 1;

    /**
     * 下载中心上传失败
     */
    public static final Integer FAIL = 2;
    /**
     * safeQuantity
     */
    public static final String SAFE_QUANTITY="safeQuantity";

    /**
     * 整数3
     */
    public static final Integer THREE = 3;

    /**
     * 整数4
     */
    public static final Integer FOUR = 4;

    /**
     * 整数7
     */
    public static final Integer SEVEN = 7;

    /**
     * 整数10
     */
    public static final Integer TEN = 10;

    /**
     * 整数14
     */
    public static final Integer FOUR_TEEN = 14;
    /**
     * 整数11
     */
    public static final Integer ELEVEN = 11;

    /**
     * 整数30
     */
    public static final Integer THIRTY = 30;

    /**
     * 分数0.8
     */
    public static final Double ZERO_POINT_EIGHT = 0.8;

    /**
     * MAX_TIME
     */
    public static final LocalTime MAX_TIME =LocalTime.of(23,59,59);

    /**
     * 缺货sku补货通知标题
     */
    public static final String PURCHASE_IN = "缺货商品已采购,待入库";
    public static final String STORE_ALLOCATION_OUT = "缺货商品已预约调拨,待入库";
    public static final String NEXT_DAY_ARRIVE  = "缺货商品已预约调拨,次日达";
    /**
     * 缺货sku补货通知最大显示条数
     */
    public static final Integer MAX_SKU_LIST = 15;

    /**
     * 离线数据库历史销量表名
     */
    public static final String STOCK_SKU_STATISTICS = "stock_sku_statistics";

    /**
     * 离线数据库未来销量表名
     */
    public static final String WAREHOUSE_ESTIMATED_CONSUMPTION = "warehouse_estimated_consumption";

    /**
     * -
     */
    public static final String DEFAULT_SYMBOL = "-";
    /**
     * M1提成系数
     */
    public static final String COMMISSION_COEFFICIENT = "commissionCoefficient";
    /**
     * 月活目标
     */
    public static final String TARGET_MONTH_LIVING = "targetOfMonthLiving";
    /**
     * gmv目标
     */
    public static final String TARGET_GMV = "targetOfGmv";

    /**
     * 市
     */
    public static final String NO_AREA_CITY = "NO_AREA_CITY";


    /**
     * reason_type:其他
     */
    public static final String OTHER = "其他";

    public static final String CHECK_CHAR = "^[ !。？，,.?A-z0-9\\u4e00-\\u9fa5]*$";

    /**
     * 精准送sku
     */
    public static final String TIME_FRAME_FEE_SKU = "DF001TF0001";



    /**
     * 加单超时
     */
    public static final String ODERA_ADD_TIMD_SKU = "DF001TF0003";

    public static final String OLD_MONTH_MERCHANT = "月活老客户";

    public static final String NEW_MONTH_MERCHANT = "月活新客户";

    /**
     * auth服务 业务系统_用户名-token关联key prefix
     */
    public static final String ORIGIN_USERNAME_TOKEN_PREFIX = "auth:origin_username_token:%s_%s";
    /**
     * shiro token key prefix
     */
    public static final String SHIRO_TOKEN_PREFIX = "shiro:session:%s";
    /**
     * AOL开发邮件组
     */
    public static final String AOL_DEVELOPER_GROUP = "<EMAIL>";

    /**
     * AOL全体邮件组
     */
    public static final String ALL_AOL_GROUP = "<EMAIL>";

    /**
     * day
     */
    public static final String DAY = "day";

    /**
     * month
     */
    public static final String MONTH = "month";

    /**
     * 左括号
     */
    public static final String LEFT_BRACKET = "(";

    /**
     * 斜线
     */
    public static final String SLASH = "/";

    /**
     * srm 默认密码
     */
    public static final String SRM_DEFAULT_PASSWORD = "xianmu1234";

    /**
     * 采购对账单详情地址
     */
    public static final String FINANCE_ACCOUNT_ADDRESS = "https://admin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details?id=";

    public static final String FINANCE_ACCOUNT_ADDRESS_CHECK = "https://admin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details";

    public static final String FINANCE_ACCOUNT_ADDRESS_QA = "https://qaadmin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details?id=";

    public static final String FINANCE_ACCOUNT_ADDRESS_QA_CHECK = "https://qaadmin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details";

    public static final String FINANCE_ACCOUNT_ADDRESS_DEV = "https://devadmin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details?id=";

    public static final String FINANCE_ACCOUNT_ADDRESS_DEV_CHECK = "https://devadmin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details";

    public static final String FINANCE_ACCOUNT_ADDRESS_DEV2 = "https://dev2admin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details?id=";

    public static final String FINANCE_ACCOUNT_ADDRESS_DEV2_CHECK = "https://dev2admin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details";

    public static final String FINANCE_ACCOUNT_ADDRESS_DEV3 = "https://dev3admin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details?id=";

    public static final String FINANCE_ACCOUNT_ADDRESS_DEV3_CHECK = "https://dev3admin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details";

    public static final String FINANCE_ACCOUNT_ADDRESS_DEV4 = "https://dev4admin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details?id=";

    public static final String FINANCE_ACCOUNT_ADDRESS_DEV4_CHECK = "https://dev4admin.summerfarm.net/summerfarm/home.html#/purchasing/purchase-statement/details";


    /**
     * 采购预付单地址
     */
    public static final String PURCHASE_ADVANCED_ORDER_ADDRESS = "https://admin.summerfarm.net/summerfarm/home.html#/purchasing/prepai-order/details?id=";

    public static final String PURCHASE_ADVANCED_ORDER_ADDRESS_CHECK = "https://admin.summerfarm.net/summerfarm/home.html#/purchasing/prepai-order/details";

    public static final String PURCHASE_ADVANCED_ORDER_ADDRESS_QA = "https://qaadmin.summerfarm.net/summerfarm/home.html#/purchasing/prepai-order/details?id=";

    public static final String PURCHASE_ADVANCED_ORDER_ADDRESS_QA_CHECK = "https://qaadmin.summerfarm.net/summerfarm/home.html#/purchasing/prepai-order/details";

    public static final String PURCHASE_ADVANCED_ORDER_ADDRESS_DEV = "https://devadmin.summerfarm.net/summerfarm/home.html#/purchasing/prepai-order/details?id=";

    public static final String PURCHASE_ADVANCED_ORDER_ADDRESS_DEV_CHECK = "https://devadmin.summerfarm.net/summerfarm/home.html#/purchasing/prepai-order/details";

    public static final String PURCHASE_ADVANCED_ORDER_ADDRESS_DEV2 = "https://dev2admin.summerfarm.net/summerfarm/home.html#/purchasing/prepai-order/details?id=";

    public static final String PURCHASE_ADVANCED_ORDER_ADDRESS_DEV2_CHECK = "https://dev2admin.summerfarm.net/summerfarm/home.html#/purchasing/prepai-order/details=";

    /**
     * BMS结算打款单单地址
     */
    public static final String BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS = "https://admin.summerfarm.net/summerfarm-fe/bms_settlement/settlement_pay_manage/detail?id=";

    public static final String BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_CHECK = "https://admin.summerfarm.net/summerfarm-fe/bms_settlement/settlement_pay_manage/detail";

    public static final String BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_QA = "https://qaadmin.summerfarm.net/summerfarm-fe/bms_settlement/settlement_pay_manage/detail?id=";

    public static final String BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_QA_CHECK = "https://qaadmin.summerfarm.net/summerfarm-fe/bms_settlement/settlement_pay_manage/detail";

    public static final String BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_DEV = "https://devadmin.summerfarm.net/summerfarm-fe/bms_settlement/settlement_pay_manage/detail?id=";

    public static final String BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_DEV_CHECK = "https://devadmin.summerfarm.net/summerfarm-fe/bms_settlement/settlement_pay_manage/detail";

    public static final String BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_DEV2 = "https://dev2admin.summerfarm.net/summerfarm-fe/bms_settlement/settlement_pay_manage/detail?id=";

    public static final String BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_DEV2_CHECK = "https://dev2admin.summerfarm.net/summerfarm-fe/bms_settlement/settlement_pay_manage/detail";


    /**
     * 200
     */
    public static final Integer TWO_HUNDRED = 200;

    /**
     * saas订单标识
     */
    public static final String OR_ORDER = "OR";

    /**
     * white space
     */
    public static final String WHITE_SPACE =" ";

    /**
     * 帮采费
     */
    public static final String PURCHASE_AMOUNT = "帮采费";
    /**
     * 打车费
     */
    public static final String TRAFFIC_AMOUNT = "打车费";
    /**
     * 点位数
     */
    public static final String TOTAL_POINT_QUANTITY = "总点位数";
    /**
     * 点位数
     */
    public static final String POINT_QUANTITY = "点位数";
    /**
     * 公里数
     */
    public static final String KM = "公里数";
    /**
     * 公里数
     */
    public static final String PATH_KM = "线路公里数";
    /**
     * 公里数
     */
    public static final String PATH_POINT_QUANTITY = "线路点位数";
    /**
     * 线路数
     */
    public static final String PATH_QUANTITY = "线路数";
    /**
     * 商品数
     */
    public static final String WARES_QUANTITY = "商品数";
    /**
     * 专车点位数
     */
    public static final String SPECIAL_POINT_QUANTITY = "专车点位数";
    /**
     * 专车公里数
     */
    public static final String SPECIAL_KM = "专车公里数";
     /**
     * 专车公里数
     */
    public static final String SPECIAL_KM_FEE = "专车公里费";
    /**
     * 专车线路数
     */
    public static final String SPECIAL_PATH_QUANTITY = "专车线路数";
    /**
     * 专车商品数
     */
    public static final String SPECIAL_WARES_QUANTITY = "专车商品数";
    /**
     * 打车点位数
     */
    public static final String TAXI_POSITION = "打车点位数";

    /**
     * 拦截点位数
     */
    public static final String INTERCEPT_POSITION = "拦截点位数";

    /**
     * 8
     */
    public static final Integer EIGHT = 8;

    /**
     * 产地属性ID
     */
    public static final Long ORIGIN = 1L;

    /**
     * 品牌属性ID
     */
    public static final Long BRAND = 2L;

    /**
     * 存储温度属性id
     */
    public static final Long STORAGE_TEMPERATURE = 3L;

    /**
     * APPLICATION_KEY
     */
    public static final String APPLICATION_KEY = "application:";

    /**
     * APPLICATION_ITEM_KEY
     */
    public static final String APPLICATION_ITEM_KEY = "applicationItem:";

    /**
     * APPLICATION_ITEM_KEY
     */
    public static final String APPLICATION_ITEMCODE_KEY = "applicationItemCode:";
    /**
     * APPLICATION_SPU_CODE
     */
    public static final String APPLICATION_SPU_CODE = "applicationSpuCode:";

    /**
     * SaaS类目Id
     */
    public static final String SAAS_CATEGORY_ID = "saas_category_id";

    /**
     * 存储温度Id
     */
    public static final String STORAGE_TEMPERATURE_PROPERTY_ID = "storage_temperature_property_id";

    /**
     * 自定义属性id
     */
    public static final String CUSTOM_PROPERTY_ID = "custom_property_id";



    /**
     * 验证手机号格式
     */
    public static final String VERIFY_PHONE = "^[1][0-9]{10}$";

    /**
     * 去除weight字段中0||1_ 是否展示平均价部分
     */
    public static final String SUB_WEIGHT = "\\d{1,5}\\.{0,1}\\d{0,2}";

    /**
     * 鲜果属性排序规则
     */
    public static final List<String> FRUIT_PROPERTY_SORT = Arrays.asList("品种","口味","熟度","产地",
            "品牌","储藏温度","储藏区域","外包装","商品形态","湿度","肉类品种","蔬菜品种","是否含糖","面筋含量","乳脂含量","使用方法",
            "成分","每100g含蛋白质","每100g乳脂含量","其他");

    /**
     * 非鲜果属性排序规则
     */
    public static final List<String> NON_FRUIT_PROPERTY_SORT = Arrays.asList("品牌","储藏温度","储藏区域",
            "乳脂含量","每100g乳脂含量","每100g含蛋白质","肉类品种","蔬菜品种","面筋含量","口味","品种","产地",
            "成分","商品形态", "使用方法","外包装","湿度","是否含糖","其他","熟度");

    public final static String CUSTOM_PROPERTY_NAME = "自定义属性";

    public final static String FILTRATION_PROPERTY = "原料/成品";

    public final static String LEVEL_PROPERTY_NAME = "级别";

    public final static String XIANMU_MAJOR_PRICE_DOWNLOAD = "-报价函";

    // 默认缺省图
    public final static String GOODS_DEFAULT = "goods-default.jpg";
}
