package net.summerfarm.manage.common.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum DistributionRulesTypeEnum {

    /**
     * 规则类型  1：门店管理  2：品牌管理  3：服务区域
     */
    MERCHANT(1,"门店管理"),
    ADMIN(2,"品牌管理"),
    AREA(3,"服务区域")
    ;
    private Integer code;

    private String value;

    DistributionRulesTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (DistributionRulesTypeEnum c : DistributionRulesTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
