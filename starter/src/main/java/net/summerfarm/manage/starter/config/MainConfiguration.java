
package net.summerfarm.manage.starter.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PostConstruct;

@Slf4j
@EnableAsync
@EnableAutoConfiguration
@DubboComponentScan("net.summerfarm.manage.**.provider")
@ComponentScan(value = {"net.summerfarm.manage.*","net.xianmu.authentication"})
@MapperScan(value = {"net.summerfarm.manage.infrastructure.mapper.**"}, annotationClass = Mapper.class)
public class MainConfiguration {

    @PostConstruct
    public void init() {
        log.info("MainConfiguration init...");
    }
}
