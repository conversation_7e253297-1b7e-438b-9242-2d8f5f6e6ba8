package net.summerfarm.manage.starter.config;

import net.xianmu.authentication.shiro.filter.PermissionFilter;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/22 18:48
 */
@Configuration
public class ShiroConfigFilter {

    /**
     * ShiroFilter是整个Shiro的入口点，用于拦截需要安全控制的请求进行处理
     */
    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);
        shiroFilter.setLoginUrl("/summerfarm/home.html#/login");
        Map<String, String> filterMap = new LinkedHashMap<>();
        filterMap.put("/ok", "anon");
        filterMap.put("/static-resources/**", "anon");
        filterMap.put("/pages/managelogin/**", "anon");
        filterMap.put("/pages/management/**", "authc");
        filterMap.put("/admin/dingTalkSync", "anon");
        filterMap.put("/plugins/**", "anon");
        filterMap.put("/bundle/**", "anon");
        filterMap.put("/static/**", "anon");
        filterMap.put("/themes/**", "anon");
        filterMap.put("/common/**", "anon");
        filterMap.put("/v2/api-docs", "anon");
        filterMap.put("/product-stock/trust-change/batch/temp", "anon");
        filterMap.put("/index.html", "anon");
        filterMap.put("/check", "anon");
        filterMap.put("/admin/send/code", "anon");
        filterMap.put("/admin/update/password", "anon");
        filterMap.put("/stock-task/transfer/download", "anon");
        filterMap.put("/messageReminder/completeDelivery/download", "anon");
        filterMap.put("/admin/upsert/default-pwd-init", "anon");
        // 后门接口放过拦截
        filterMap.put("/inccddaa/**", "anon");
        filterMap.put("/swagger*", "anon");
        filterMap.put("/dingding/event/**", "anon");
        //外部对接，下单服务
        filterMap.put("/api/orderService/placeOrder", "anon");
        filterMap.put("/api/outerQuery/**", "anon");
        filterMap.put("/api/query/query-delivery-time", "anon");
        filterMap.put("/api/outerPost/**", "anon");
        filterMap.put("/api/query-city-supply-status", "anon");
        //扫码界面
        filterMap.put("/skuBatchCod/code/**", "anon");
        filterMap.put("/skuBatchCod/query/**", "anon");
        //出仓监控
        filterMap.put("/tms/outTimeMonitor/download", "anon");
        //saas服务
        filterMap.put("/outSideOrder/**", "anon");
        filterMap.put("/summerfarm-manage/**", "anon");
        filterMap.put("/common/devtool/**", "anon");

        // 同义词词典下载接口
        filterMap.put("/synonyms/download/synonyms.txt", "anon");

        // 商品AI扩展接口
        filterMap.put("/marketItemAI4ProductInfo/**", "anon");

        //查询任务状态
        filterMap.put("/stock-task/query/task-status", "anon");
        //订单拦截查询接口
        filterMap.put("/order/delivery-path/queryOrderInterception", "anon");
        filterMap.put("/order/delivery-path/sendOrderInterceptionNew", "anon");

        filterMap.put("/**", "authc");
        shiroFilter.setFilterChainDefinitionMap(filterMap);

        Map<String, Filter> filterWonMap = new LinkedHashMap<>();
        filterWonMap.put("authc", new PermissionFilter());
        shiroFilter.setFilters(filterWonMap);
        return shiroFilter;
    }
}
