<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
    </parent>
    <groupId>net.summerfarm</groupId>
    <artifactId>mall-manage</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <app.version>1.0.0-SNAPSHOT</app.version>
        <app.client.version>1.2.6-RELEASE</app.client.version>
        <summerfarm-common.version>1.5.10-RELEASE</summerfarm-common.version>
        <xianmu-common.version>1.1.6-RELEASE</xianmu-common.version>
        <xianmu-dubbo.version>1.0.9</xianmu-dubbo.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <xianmu-oss.version>1.0.2</xianmu-oss.version>
        <xianmu-mq.version>1.2.1</xianmu-mq.version>
        <xianmu-robot-util.version>1.0.2</xianmu-robot-util.version>
        <summerfarm-wnc-client.version>1.4.1-RELEASE</summerfarm-wnc-client.version>
        <goods-center.version>1.2.5-RELEASE</goods-center.version>
        <scp-service.version>1.0.6</scp-service.version>
        <auth-client.version>1.1.25</auth-client.version>
        <pms-client.version>1.5.9-RELEASE</pms-client.version>
        <wms-client.version>1.7.5-RELEASE</wms-client.version>

        <fastjson.version>1.2.83</fastjson.version>
        <lombok.version>1.18.2</lombok.version>
        <starter.version>2.1.1</starter.version>
        <page.version>1.2.7</page.version>
        <mysql-connector.version>8.0.23</mysql-connector.version>
        <druid.version>1.1.20</druid.version>
        <easyexcel.version>3.2.1</easyexcel.version>
        <dubbo-registry-nacos.version>2.7.15</dubbo-registry-nacos.version>
        <rocketmq.starter.version>2.1.1</rocketmq.starter.version>
        <redisson.version>3.11.1</redisson.version>
        <auth-sdk.version>1.1.5</auth-sdk.version>
        <nacos-config.version>0.2.12</nacos-config.version>
        <mall-client.version>1.0.27-RELEASE</mall-client.version>
        <usercenter-client.version>1.2.3</usercenter-client.version>
        <marketing-center-client.version>1.0.9-RELEASE</marketing-center-client.version>
        <inventory-client.verison>2.0.24-RELEASE</inventory-client.verison>
        <inventory-sdk.verison>2.0.9-RELEASE</inventory-sdk.verison>
        <job-sdk.verison>1.0.6-RELEASE</job-sdk.verison>
        <task.version>1.0.5</task.version>
        <tms-client.version>1.0.12-RELEASE</tms-client.version>
        <message-client.version>1.3.1-RELEASE</message-client.version>
        <pagehelper.version>5.1.6</pagehelper.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>

        <cosfo-manage-client.verison>1.2.4-RELEASE</cosfo-manage-client.verison>
        <cosfo-item-client.verison>1.0.39-RELEASE</cosfo-item-client.verison>
        <taobao.verison>1.1.0</taobao.verison>
    </properties>

    <dependencyManagement>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependencies>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-manage-common</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-manage-facade</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-manage-domain</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-manage-application</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-manage-infrastructure</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-manage-starter</artifactId>
                <version>${app.version}</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>sf-mall-manage-client</artifactId>
                <version>${app.client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <version>${xianmu-common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-oss-support</artifactId>
                <version>${xianmu-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>${xianmu-dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>${xianmu-log.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-sdk</artifactId>
                <version>${auth-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-common</artifactId>
                <version>${summerfarm-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.rocketmq</groupId>
                        <artifactId>rocketmq-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>qiniu-java-sdk</artifactId>
                        <groupId>com.qiniu</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>okio</artifactId>
                        <groupId>com.squareup.okio</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mysql-connector-java</artifactId>
                        <groupId>mysql</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-datatype-jsr310</artifactId>
                        <groupId>com.fasterxml.jackson.datatype</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>elasticsearch-rest-high-level-client</artifactId>
                        <groupId>org.elasticsearch.client</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>elasticsearch-rest-client</artifactId>
                        <groupId>org.elasticsearch.client</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>elasticsearch</artifactId>
                        <groupId>org.elasticsearch</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>net.xianmu.common</groupId>
                        <artifactId>xianmu-robot-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-client</artifactId>
                <version>${usercenter-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-client</artifactId>
                <version>${marketing-center-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-client</artifactId>
                <version>${summerfarm-wnc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-client</artifactId>
                <version>${mall-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${task.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-client</artifactId>
                <version>${tms-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>message-client</artifactId>
                <version>${message-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-robot-util</artifactId>
                <version>${xianmu-robot-util.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>summerfarm-inventory-client</artifactId>
                <version>${inventory-client.verison}</version>
            </dependency>
            <dependency>
                <artifactId>summerfarm-inventory-sdk</artifactId>
                <groupId>net.summerfarm</groupId>
                <version>${inventory-sdk.verison}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm.wnc</groupId>
                        <artifactId>summerfarm-wnc-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>job-sdk</artifactId>
                <version>${job-sdk.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>cosfo-manage-client</artifactId>
                <version>${cosfo-manage-client.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>item-center-client</artifactId>
                <version>${cosfo-item-client.verison}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>goods-center-client</artifactId>
                <version>${goods-center.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>scp-service-client</artifactId>
                <version>${scp-service.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-client</artifactId>
                <version>${auth-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-pms-client</artifactId>
                <version>${pms-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>xianmu-download-support</artifactId>
                <version>1.0.8</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>common-client</artifactId>
                <version>1.1.1-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-oss-support</artifactId>
                <version>1.0.7</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm.wms</groupId>
                <artifactId>summerfarm-wms-client</artifactId>
                <version>${wms-client.version}</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->



            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <!-- 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${page.version}</version>
            </dependency>
            <!-- 数据库组件——mysql连接组件 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- alibaba开源数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 注册中心 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo-registry-nacos.version}</version>
            </dependency>
            <!-- rocket mq -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.starter.version}</version>
            </dependency>
            <!-- alibaba json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--  lombok  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.11</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <dependency>
                <groupId>org.taobao.dingtalk</groupId>
                <artifactId>dingtalk</artifactId>
                <version>${taobao.verison}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <propertyFile>archetype.properties</propertyFile>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <!-- This is needed when using Lombok 1.18.16 and above -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>    

<modules>  <module>application</module>
    <module>domain</module>
    <module>infrastructure</module>
    <module>common</module>
    <module>facade</module>
    <module>starter</module>
  </modules>
</project>
