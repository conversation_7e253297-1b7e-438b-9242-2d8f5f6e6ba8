package net.summerfarm.manage.facade.deliivery.converter;

import net.summerfarm.manage.common.enums.FenceStatusEnum;
import net.summerfarm.manage.facade.deliivery.dto.ContactBelongFenceDTO;
import net.summerfarm.manage.facade.deliivery.input.ContactFenceQueryInput;
import net.summerfarm.wnc.client.req.AddressQueryReq;
import net.summerfarm.wnc.client.req.fence.ContactAddressQueryReq;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.manage.facade.deliivery.dto.ContactDTO;
import net.summerfarm.wnc.client.resp.deliveryRule.XmContactDeliveryRuleResp;
import net.summerfarm.wnc.client.resp.fence.ContactAddressBelongFenceResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/11/14 16:01:44
 */
public class ContactConverter {


    public static ContactDTO toContactDTO(XmContactDeliveryRuleResp resp) {
        if (resp == null) {
            return null;
        }
        ContactDTO contactDTO = new ContactDTO();
        contactDTO.setContactId(Long.valueOf(resp.getOutBusinessNo()));
        contactDTO.setBeginCalculateDate(resp.getBeginCalculateDate());
        contactDTO.setDeliveryFrequentInterval(resp.getDeliveryFrequentInterval());
        contactDTO.setFrequentMethod(resp.getFrequentMethod());
        contactDTO.setWeekDeliveryFrequent(resp.getWeekDeliveryFrequent());
        return contactDTO;
    }

    public static List<ContactAddressQueryReq> contactFenceQueryInputToBatchQueryReq(List<ContactFenceQueryInput> inputs) {
        if (CollectionUtils.isEmpty(inputs)) {
            return null;
        }

        List<ContactAddressQueryReq> contactAddressQueryReqList = new ArrayList<>(inputs.size());
        inputs.stream().forEach(e -> {
            ContactAddressQueryReq contactAddressQueryReq = new ContactAddressQueryReq();
            AddressQueryReq addressQueryReq = new AddressQueryReq();

            addressQueryReq.setAddress(e.getAddress());
            addressQueryReq.setArea(e.getArea());
            addressQueryReq.setCity(e.getCity());
            addressQueryReq.setProvince(e.getProvince());

            contactAddressQueryReq.setContactId(e.getContactId());
            contactAddressQueryReq.setAddressReq(addressQueryReq);
            contactAddressQueryReqList.add(contactAddressQueryReq);
        });
        return contactAddressQueryReqList;
    }

    public static void toContactBelongFenceDTOMap(String fenceChannelType, Map<Long, ContactBelongFenceDTO> map, List<ContactAddressBelongFenceResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        data.stream().forEach(e -> {
            ContactBelongFenceDTO contactBelongFenceRes = new ContactBelongFenceDTO();
            contactBelongFenceRes.setContactId(e.getContactId());
            if (Objects.isNull(e.getDeliveryFenceResp())) {
                contactBelongFenceRes.setStatus(FenceStatusEnum.PAUSE.getCode());
            } else {
                DeliveryFenceResp deliveryFenceResp = e.getDeliveryFenceResp();
                contactBelongFenceRes.setFenceName(deliveryFenceResp.getFenceName());
                List<String> fenceChannelList = null;

                //查询围栏渠道
                if (StringUtils.isNotBlank(e.getDeliveryFenceResp().getOrderChannelType())) {
                    String[] fenceChannels = e.getDeliveryFenceResp().getOrderChannelType().split(",");
                    fenceChannelList = Arrays.asList(fenceChannels);
                }

                //设置围栏状态 假如围栏状态是开启 判断当前客户是否再围栏开启的渠道内
                if (Objects.equals(deliveryFenceResp.getStatus(), FenceStatusEnum.OPEN.getCode())) {
                    contactBelongFenceRes.setStatus(FenceStatusEnum.OPEN.getCode());
                    if (!CollectionUtils.isEmpty(fenceChannelList) && !fenceChannelList.contains(fenceChannelType)) {
                        contactBelongFenceRes.setStatus(FenceStatusEnum.PAUSE.getCode());
                    }
                } else {
                    contactBelongFenceRes.setStatus(FenceStatusEnum.PAUSE.getCode());
                }
            }
            map.put(e.getContactId(), contactBelongFenceRes);
        });
    }
}
