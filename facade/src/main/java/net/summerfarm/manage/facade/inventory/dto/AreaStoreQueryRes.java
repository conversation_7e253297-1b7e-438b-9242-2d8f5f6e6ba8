package net.summerfarm.manage.facade.inventory.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-16
 */
@Data
public class AreaStoreQueryRes implements Serializable {

    /**
     * 租户编号
     */
    public Integer tenantId;

    /**
     * 仓库编码
     */
    public Long warehouseNo;

    /**
     * 仓库所属租户编码
     */
    public Integer warehouseTenantId;

    /**
     * 货品编码
     */
    public String skuCode;

    /**
     * 货品租户编码
     */
    public Integer skuTenantId;

    /**
     * 仓库数量
     */
    public Integer quantity;

    /**
     * 虚拟库存数量
     */
    public Integer onlineQuantity;

    /**
     * 可用数量
     */
    public Integer availableQuantity;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 截单时间
     */
    private LocalTime closeTime;

    /**
     * 截单具体时间
     */
    private LocalDateTime deliveryCloseTime;

    /**
     *是否日配的标识 0日配，1非日配
     */
    private Integer isEveryDayFlag;

    /**
     *商品子类类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销
     */
    private Integer skuSubType;

    /**
     * 预留库存最大值
     */
    private Integer reserveMaxQuantity;

    /**
     * 预留库存最小值
     */
    private Integer reserveMinQuantity;

    /**
     * 预留库存最大值大于0（支持预留）大客户下单就会累加
     * 在线库存展示逻辑： 实际使用 > 最大值减最小值 展示 最大值减最小值
     * 反之 展示预留库存实际使用数量
     */
    private Integer reserveUseQuantity;


    /**
     * 采购价
     */
    private BigDecimal costPrice;
}
