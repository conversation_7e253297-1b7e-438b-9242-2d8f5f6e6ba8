package net.summerfarm.manage.facade.deliivery.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName DistDeliveryBatchDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 18:37 2024/1/17
 * @Version 1.0
 **/
@Data
public class DistDeliveryBatchDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 批次号(调度单号)
     */
    private Long deliveryBatchId;

    /**
     * 履约时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 司机名称
     */
    private String driver;

    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 线路名称
     */
    private String pathName;

    /**
     * 路线编码
     */
    private String pathCode;
}
