package net.summerfarm.manage.facade.inventory;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.constants.Global;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.SaleInventoryCenterQueryProvider;
import net.xianmu.inventory.client.saleinventory.dto.req.QueryWarehouseSkuInventoryReq;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryResp;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: xiaowk
 * @time: 2024/7/31 上午11:27
 */
@Slf4j
@Component
public class SaleInventoryCenterQueryFacade {

    @DubboReference
    private SaleInventoryCenterQueryProvider saleInventoryCenterQueryProvider;


    public List<WarehouseSkuInventoryDetailResDTO> queryWarehouseSkuInventoryList(Map<String, Set<Integer>> skuWarehouseNoMap) {
        if(CollectionUtil.isEmpty(skuWarehouseNoMap)) {
            log.warn("请求参数为空!");
            return Collections.emptyList();
        }

        List<WarehouseSkuInventoryDetailResDTO> result = new ArrayList<>();
        skuWarehouseNoMap.forEach((k, v) -> result.addAll(queryWarehouseSkuInventoryList(k, v)));
        return result;
    }


    public List<WarehouseSkuInventoryDetailResDTO> queryWarehouseSkuInventoryList(String sku, Set<Integer> warehouseList) {
        QueryWarehouseSkuInventoryReq queryWarehouseSkuInventoryReq = new QueryWarehouseSkuInventoryReq();
        queryWarehouseSkuInventoryReq.setWarehouseNoList(new ArrayList<>(warehouseList));
        queryWarehouseSkuInventoryReq.setSkuCode(sku);
        queryWarehouseSkuInventoryReq.setTenantId(Global.XM_TENANTID);
        DubboResponse<WarehouseSkuInventoryResp> response = saleInventoryCenterQueryProvider.queryWarehouseSkuInventory(queryWarehouseSkuInventoryReq);
        if (Objects.isNull(response) || !DubboResponse.SUCCESS_STATUS.equals(response.getStatus())) {
            log.warn("queryWarehouseSkuInventory error warehouseList:{}, sku:{},response={}", JSON.toJSONString(warehouseList), sku, JSON.toJSONString(response));
            return Collections.emptyList();
        }
        if (response.getData() != null) {
            return response.getData().getWarehouseSkuInventoryDetailResDTOS();
        }
        return Collections.emptyList();
    }


    /**
     * 根据sku + warehouseNo 查询仓库库存
     *
     * @param warehouseNo
     * @param sku
     * @return
     */
    public WarehouseSkuInventoryDetailResDTO queryWarehouseSkuInventory(Integer warehouseNo, String sku) {
        if (StringUtils.isBlank(sku) || warehouseNo == null) {
            log.warn("参数为空异常：skuList:{}, warehouseNo:{}", sku, warehouseNo);
            return null;
        }

        QueryWarehouseSkuInventoryReq req = new QueryWarehouseSkuInventoryReq();
        req.setTenantId(Global.XM_TENANTID);
        req.setWarehouseNoList(Lists.newArrayList(warehouseNo));
        req.setSkuCode(sku);
        DubboResponse<WarehouseSkuInventoryResp> response = saleInventoryCenterQueryProvider.queryWarehouseSkuInventory(req);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }

        if (response.getData() == null || CollectionUtils.isEmpty(response.getData().getWarehouseSkuInventoryDetailResDTOS())) {
            return null;
        }

        return response.getData().getWarehouseSkuInventoryDetailResDTOS().get(0);
    }

}

