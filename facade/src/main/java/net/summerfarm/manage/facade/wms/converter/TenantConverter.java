package net.summerfarm.manage.facade.wms.converter;

import com.cosfo.manage.client.tenant.resp.TenantResp;
import net.summerfarm.manage.facade.wms.dto.TenantDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName TenantConverter
 * @Description
 * <AUTHOR>
 * @Date 13:43 2024/5/6
 * @Version 1.0
 **/
public class TenantConverter {

    public static List<TenantDTO> tenantRespList2TenantDTOList(List<TenantResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        List<TenantDTO> tenantDTOS = new ArrayList<>(data.size());
        data.stream().forEach(tenantResp -> {
            TenantDTO tenantDTO = new TenantDTO();
            tenantDTO.setId(tenantResp.getId());
            tenantDTO.setTenantName(tenantResp.getTenantName());
            tenantDTO.setAdminId(tenantResp.getAdminId());
            tenantDTO.setPhone(tenantResp.getPhone());
            tenantDTO.setCompanyName(tenantResp.getCompanyName());
            tenantDTOS.add(tenantDTO);
        });
        return tenantDTOS;
    }
}
