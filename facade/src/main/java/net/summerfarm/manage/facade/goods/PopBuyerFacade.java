package net.summerfarm.manage.facade.goods;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.PopBuyerProvider;
import net.summerfarm.goods.client.req.PopBuyerQueryReq;
import net.summerfarm.goods.client.req.QueryPopBuyerByAdminIdListReq;
import net.summerfarm.goods.client.resp.PopBuyerQueryResp;
import net.summerfarm.manage.facade.goods.converter.PopBuyerConvert;
import net.summerfarm.manage.facade.goods.dto.PopBuyerInfoDTO;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2024/7/3 18:01
 * @<AUTHOR>
 */
@Service
@Slf4j
public class PopBuyerFacade {

    @DubboReference
    private PopBuyerProvider popBuyerProvider;

    /**
     * 根据买手id批量查询买手信息
     *
     * <AUTHOR>
     * @date 2024/7/1 15:34
     * @param buyerIdList 买手id列表
     * @return java.util.List<PopBuyerInfoDTO>
     */
    public List<PopBuyerInfoDTO> queryPopBuyerByIdList(List<Long> buyerIdList) {
        if (CollectionUtils.isEmpty(buyerIdList)) {
            return Lists.newArrayList();
        }
        PopBuyerQueryReq queryReq = PopBuyerQueryReq
                .builder().buyerIdList(buyerIdList).build();
        DubboResponse<PopBuyerQueryResp> resp = popBuyerProvider.queryPopBuyerByIdList(queryReq);
        if(!resp.isSuccess()){
            log.error("调用货品中心查询买手信息异常 buyerIdList:{} resp:{}", buyerIdList, JSON.toJSONString(resp));
            throw new ProviderException("调用货品中心查询买手信息异常:" + resp.getMsg());
        }
        PopBuyerQueryResp popBuyerQueryResp = resp.getData();
        if (Objects.isNull(popBuyerQueryResp) || CollectionUtils.isEmpty(popBuyerQueryResp.getPopBuyerDTOList())) {
            return Lists.newArrayList();
        }
        return popBuyerQueryResp.getPopBuyerDTOList().stream().map(PopBuyerConvert::convert).collect(Collectors.toList());
    }

    /**
     * 根据admin id批量查询买手信息
     *
     * <AUTHOR>
     * @date 2024/7/3 11:54
     * @param adminIdList adminId列表
     * @return java.util.List<net.summerfarm.facade.goods.dto.PopBuyerInfoDTO>
     */
    public List<PopBuyerInfoDTO> queryPopBuyerByAdminIdList(List<Long> adminIdList) {
        if (CollectionUtils.isEmpty(adminIdList)) {
            return Lists.newArrayList();
        }
        QueryPopBuyerByAdminIdListReq queryReq = QueryPopBuyerByAdminIdListReq
                .builder().adminIdList(adminIdList).build();
        DubboResponse<PopBuyerQueryResp> resp = popBuyerProvider.queryPopBuyerByAdminIdList(queryReq);
        if(!resp.isSuccess()){
            log.error("调用货品中心查询买手信息异常 adminIdList:{} resp:{}", adminIdList, JSON.toJSONString(resp));
            throw new ProviderException("调用货品中心查询买手信息异常:" + resp.getMsg());
        }
        PopBuyerQueryResp popBuyerQueryResp = resp.getData();
        if (Objects.isNull(popBuyerQueryResp) || CollectionUtils.isEmpty(popBuyerQueryResp.getPopBuyerDTOList())) {
            return Lists.newArrayList();
        }
        return popBuyerQueryResp.getPopBuyerDTOList().stream().map(PopBuyerConvert::convert).collect(Collectors.toList());
    }

}
