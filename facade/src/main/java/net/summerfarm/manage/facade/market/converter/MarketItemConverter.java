package net.summerfarm.manage.facade.market.converter;

import com.cofso.item.client.resp.MarketItemDetailResp;
import com.cofso.item.client.resp.MarketItemLabelResp;
import net.summerfarm.manage.facade.market.dto.MarketItemDetailDTO;
import net.summerfarm.manage.facade.market.dto.MarketItemLabelDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName MarketItemConverter
 * @Description
 * <AUTHOR>
 * @Date 14:47 2024/5/8
 * @Version 1.0
 **/
public class MarketItemConverter {

    public static MarketItemLabelDTO itemLabelResToDTO(MarketItemLabelResp resp) {
        if (resp == null) {
            return null;
        }
        MarketItemLabelDTO itemLabelDTO = new MarketItemLabelDTO();
        itemLabelDTO.setTenantId(resp.getTenantId());
        itemLabelDTO.setLabelName(resp.getLabelName());
        itemLabelDTO.setLabelStatus(resp.getLabelStatus());
        itemLabelDTO.setId(resp.getId());
        return itemLabelDTO;
    }

    public static List<MarketItemLabelDTO> itemLabelResToDTOS(List<MarketItemLabelResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        List<MarketItemLabelDTO> itemLabelDTOS = new ArrayList<>(data.size());
        data.stream().forEach(resp -> itemLabelDTOS.add(itemLabelResToDTO(resp)));
        return itemLabelDTOS;
    }

    public static List<MarketItemDetailDTO> marketItemDetailResp2DTOS(List<MarketItemDetailResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        List<MarketItemDetailDTO> marketItemDetailDTOS = new ArrayList<>(data.size());
        data.stream().forEach(resp -> {
            MarketItemDetailDTO marketItemDetailDTO = new MarketItemDetailDTO();
            marketItemDetailDTO.setOutId(resp.getOutId());
            marketItemDetailDTO.setItemLabel(resp.getItemLabel());
            marketItemDetailDTOS.add(marketItemDetailDTO);
        });
        return marketItemDetailDTOS;
    }
}
