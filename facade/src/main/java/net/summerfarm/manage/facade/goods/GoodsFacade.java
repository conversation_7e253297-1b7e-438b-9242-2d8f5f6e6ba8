package net.summerfarm.manage.facade.goods;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.GoodsCodeProvider;
import net.summerfarm.goods.client.req.GoodsCodeInputReq;
import net.summerfarm.goods.client.resp.GoodsCodeResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> chenjie
 * @date : 2023-11-06 16:33
 * @describe :
 */
@Service
@Slf4j
public class GoodsFacade {
    @DubboReference
    private GoodsCodeProvider skuCodeProvider;

    public GoodsCodeResp takeSkuCode(GoodsCodeInputReq inputReq) {
        log.info("生成货品编码入参为:{}", JSON.toJSONString(inputReq));
        DubboResponse<GoodsCodeResp> response = skuCodeProvider.takeGoodsCode(inputReq);
        if(!response.isSuccess()){
            throw new ProviderException("货品中心生成SKU编码失败:"+response.getMsg());
        }
        return response.getData();
    }


}
