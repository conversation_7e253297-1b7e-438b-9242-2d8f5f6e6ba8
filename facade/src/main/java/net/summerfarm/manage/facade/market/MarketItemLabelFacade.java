package net.summerfarm.manage.facade.market;

import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.MarketItemLabelStatusEnum;
import com.cofso.item.client.provider.MarketItemLabelProvider;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.req.MarketItemDetailQueryReq;
import com.cofso.item.client.req.MarketItemInputReq;
import com.cofso.item.client.req.MarketItemLabelInsertReq;
import com.cofso.item.client.req.MarketItemLabelQueryReq;
import com.cofso.item.client.resp.MarketItemDetailResp;
import com.cofso.item.client.resp.MarketItemLabelResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.facade.market.converter.MarketItemConverter;
import net.summerfarm.manage.facade.market.dto.MarketItemDetailDTO;
import net.summerfarm.manage.facade.market.dto.MarketItemLabelDTO;
import net.summerfarm.manage.facade.market.input.ItemLabelSaveInput;
import net.summerfarm.manage.facade.market.input.MarketItemDetailQueryInput;
import net.summerfarm.manage.facade.market.input.MarketItemLabelInsertInput;
import net.summerfarm.manage.facade.market.input.MarketItemLabelQueryLabel;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

import static net.summerfarm.manage.common.constants.Global.XM_TENANTID;

/**
 * @ClassName MarketItemFacade
 * @Description
 * <AUTHOR>
 * @Date 14:39 2024/5/8
 * @Version 1.0
 **/
@Slf4j
@Component
public class MarketItemLabelFacade {

    @DubboReference
    private MarketItemProvider marketItemProvider;

    @DubboReference
    private MarketItemLabelProvider marketItemLabelProvider;


    public MarketItemLabelDTO insertItemLabel(MarketItemLabelInsertInput insertInput) {
        log.info("MarketItemFacade[]insertItemLabel[]start[]insertInput:{}", JSON.toJSONString(insertInput));
        MarketItemLabelInsertReq req = new MarketItemLabelInsertReq();
        req.setItemName(insertInput.getLabelName());
        req.setTenantId(insertInput.getTenantId());
        req.setLabelStatus(MarketItemLabelStatusEnum.ENABLE.getType());
        DubboResponse<MarketItemLabelResp> dubboResponse = marketItemLabelProvider.saveMarketItemLabel(req);
        if (null == dubboResponse || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())) {
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        log.info("MarketItemFacade[]insertItemLabel[]end[]dubboResponse:{}", JSON.toJSONString(dubboResponse));
        return MarketItemConverter.itemLabelResToDTO(dubboResponse.getData());
    }

    public List<MarketItemLabelDTO> allItemLabel(MarketItemLabelQueryLabel queryLabel) {
        log.info("MarketItemFacade[]allItemLabel[]start[]queryLabel:{}", JSON.toJSONString(queryLabel));
        MarketItemLabelQueryReq req = new MarketItemLabelQueryReq();
        req.setLabelName(queryLabel.getLabelName());
        req.setTenantId(XM_TENANTID);
        DubboResponse<List<MarketItemLabelResp>> dubboResponse = marketItemLabelProvider.queryMarketItemLabel(req);
        if (null == dubboResponse || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())) {
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        log.info("MarketItemFacade[]allItemLabel[]end[]dubboResponse:{}", JSON.toJSONString(dubboResponse));
        return MarketItemConverter.itemLabelResToDTOS(dubboResponse.getData());
    }

    public Boolean saveItemLabel(ItemLabelSaveInput saveInput) {
        log.info("MarketItemFacade[]saveItemLabel[]start[]queryLabel:{}", JSON.toJSONString(saveInput));
        MarketItemInputReq req = new MarketItemInputReq();
        req.setItemCode(saveInput.getItemCode());
        req.setTenantId(saveInput.getTenantId() == null ? XM_TENANTID : saveInput.getTenantId());
        req.setItemLabel(saveInput.getItemLabel());
        req.setOutId(saveInput.getOutId());
        DubboResponse<Boolean> dubboResponse = marketItemProvider.saveItemLabel(req);
        if (null == dubboResponse || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())) {
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        log.info("MarketItemFacade[]saveItemLabel[]end[]dubboResponse:{}", JSON.toJSONString(dubboResponse));
        return dubboResponse.getData();
    }

    public List<MarketItemDetailDTO> getItemDetailByOutId(MarketItemDetailQueryInput input) {
        log.info("MarketItemFacade[]getItemDetailByOutId[]start[]input:{}", JSON.toJSONString(input));
        MarketItemDetailQueryReq req = new MarketItemDetailQueryReq();
        req.setOutIds(input.getOutIds());
        DubboResponse<List<MarketItemDetailResp>> dubboResponse = marketItemProvider.getItemDetailByOutId(req);
        if (null == dubboResponse || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())) {
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        log.info("MarketItemFacade[]getItemDetailByOutId[]end[]dubboResponse:{}", JSON.toJSONString(dubboResponse));
        return MarketItemConverter.marketItemDetailResp2DTOS(dubboResponse.getData());
    }
}
