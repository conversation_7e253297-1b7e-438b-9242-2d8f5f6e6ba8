package net.summerfarm.manage.facade.goods;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.req.ProductSkuListReq;
import net.summerfarm.goods.client.req.ProductSkuPageQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.manage.common.constants.AppConsts;
import net.summerfarm.manage.facade.goods.converter.GoodsInfoDTOConvert;
import net.summerfarm.manage.facade.goods.dto.GoodsInfoDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/5/5 13:53
 * @Description:
 */
@Service
@Slf4j
public class ProductFacade {

    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;

    /**
     * 查询sku信息
     *
     * @param skus
     * @return
     */
    public List<ProductSkuBaseResp> querySkuInfoBySkuList(List<String> skus) {
        ProductSkuListReq req = new ProductSkuListReq();
        req.setTenantId(AppConsts.XIANMU_TENANT_ID);
        req.setSkuList(skus);
        DubboResponse<List<ProductSkuBaseResp>> resp = productsSkuQueryProvider.selectProductSkuBaseList(req);
        if (!resp.isSuccess()) {
            log.error("调用货品中心查询货品信息异常 skus:{} resp:{}", skus, JSON.toJSONString(resp));
            throw new ProviderException("调用货品中心查询买手信息异常:" + resp.getMsg());
        }
        return resp.getData();
    }

    /**
     * 通过skus查询货品信息
     *
     * @param skus
     * @return
     */
    public List<GoodsInfoDTO> listGoodsInfoBySkus(List<String> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return Lists.newArrayList();
        }
        Long tenantId = AppConsts.XIANMU_TENANT_ID;

        List<ProductSkuDetailResp> res = Lists.newArrayList();

        int pageNum = NumberUtils.INTEGER_ONE;
        int pageSize = 100;
        Lists.partition(skus, pageSize).forEach(skuList -> {
            ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
            productSkuPageQueryReq.setTenantId(tenantId);
            productSkuPageQueryReq.setSkuList(skuList);
            productSkuPageQueryReq.setIsSearchXmAgent(true);

            productSkuPageQueryReq.setPageIndex(pageNum);
            productSkuPageQueryReq.setPageSize(pageSize);

            try {
                DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
                if (!pageInfoDubboResponse.isSuccess()) {
                    log.error("查询货品信息失败, skus:{}, resp:{}", skuList, JSON.toJSONString(pageInfoDubboResponse));
                    return;
                }
                if (Objects.isNull(pageInfoDubboResponse.getData())) {
                    return;
                }
                List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }

                res.addAll(list);
            } catch (Throwable e) {
                log.error("查询货品信息异常, skus:{}", JSON.toJSONString(skuList), e);
                throw new BizException("查询货品信息异常");
            }
        });

        return res.stream().map(GoodsInfoDTOConvert::convertXM).collect(Collectors.toList());
    }
}
