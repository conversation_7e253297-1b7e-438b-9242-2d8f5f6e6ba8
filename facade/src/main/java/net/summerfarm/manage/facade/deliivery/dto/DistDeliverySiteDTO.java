package net.summerfarm.manage.facade.deliivery.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DistDeliverySiteDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 18:37 2024/1/17
 * @Version 1.0
 **/
@Data
public class DistDeliverySiteDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 配送点位id
     */
    private Long id;

    /**
     * 配送序号
     */
    private Integer sequence;

    /**
     * 10未到站,25已拣货,22.已配送
     */
    private Integer status;

    /**
     * 配送照片
     */
    private String signInPics;

    /**
     * 配送照片签收面单
     */
    private String signInSignPic;

    /**
     * 配送照片货物照片
     */
    private String signInProductPic;
}
