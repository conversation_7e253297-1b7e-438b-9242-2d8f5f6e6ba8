package net.summerfarm.manage.facade.pms.converter;

import net.summerfarm.manage.facade.pms.dto.PriceConfigQueryDTO;
import net.summerfarm.manage.facade.pms.dto.PriceConfigStepDTO;
import net.summerfarm.manage.facade.pms.input.PriceConfigQueryInput;
import net.summerfarm.pms.client.req.PriceConfigQueryReq;
import net.summerfarm.pms.client.resp.PriceConfigQueryResp;
import net.summerfarm.pms.client.resp.dto.PriceConfigDTO;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/20 14:29
 * @PackageName:net.summerfarm.manage.facade.pms.converter
 * @ClassName: PriceConfigConverter
 * @Description: TODO
 * @Version 1.0
 */
public class PriceConfigConverter {
    public static PriceConfigQueryReq toPriceConfigQueryReq(PriceConfigQueryInput input) {
        if (input == null) {
            return null;
        }
        PriceConfigQueryReq req = PriceConfigQueryReq.builder()
                .supplierId(input.getSupplierId())
                .warehouseNo(input.getWarehouseNo())
                .skuList(input.getSkuList()).build();
        return req;
    }

    public static Map<String, List<PriceConfigQueryDTO>> toMap(PriceConfigQueryResp data) {
        if (data == null || CollectionUtils.isEmpty(data.getPriceConfigList())) {
            return Collections.emptyMap();
        }
        Map<String, List<PriceConfigQueryDTO>> result = new HashMap<>();
        Map<String, List<PriceConfigDTO>> priceConfigMap = data.getPriceConfigList().stream().collect(Collectors.groupingBy(PriceConfigDTO::getSku));
        for (String sku : priceConfigMap.keySet()) {
            List<PriceConfigDTO> priceConfigDTOS = priceConfigMap.get(sku);
            if (CollectionUtils.isEmpty(priceConfigDTOS)) {
                continue;
            }

            List<PriceConfigQueryDTO> priceConfigQueryDTOS = new ArrayList<>();
            for (PriceConfigDTO priceConfigDTO : priceConfigDTOS) {
                if (CollectionUtils.isEmpty(priceConfigDTO.getStepPriceList())) {
                    continue;
                }
                if (Objects.equals(priceConfigDTO.getEffectStatus(), "IN_EFFECT")) {
                    PriceConfigQueryDTO pmsSupplierPriceConfigDTO = new PriceConfigQueryDTO();
                    pmsSupplierPriceConfigDTO.setSupplierId(priceConfigDTO.getSupplierId());
                    pmsSupplierPriceConfigDTO.setWarehouseNo(priceConfigDTO.getWarehouseNo());
                    pmsSupplierPriceConfigDTO.setSku(priceConfigDTO.getSku());

                    List<PriceConfigStepDTO> stepPriceList = new ArrayList<>();
                    priceConfigDTO.getStepPriceList().forEach(stepPrice -> {
                        PriceConfigStepDTO dto = new PriceConfigStepDTO();
                        dto.setPrice(stepPrice.getPrice());
                        dto.setQuantity(stepPrice.getQuantity());
                        stepPriceList.add(dto);
                    });
                    pmsSupplierPriceConfigDTO.setStepPriceList(stepPriceList);
                    priceConfigQueryDTOS.add(pmsSupplierPriceConfigDTO);
                }
            }
            result.put(sku, priceConfigQueryDTOS);
        }
        return result;
    }
}
