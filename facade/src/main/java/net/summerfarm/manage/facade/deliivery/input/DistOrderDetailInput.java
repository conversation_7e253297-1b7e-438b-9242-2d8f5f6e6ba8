package net.summerfarm.manage.facade.deliivery.input;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @ClassName DistOrderDetailInput
 * @Description TODO
 * <AUTHOR>
 * @Date 13:37 2024/1/18
 * @Version 1.0
 **/
@Data
public class DistOrderDetailInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址ID
     */
    private Long contactId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 来源
     */
    private Integer source;
}
