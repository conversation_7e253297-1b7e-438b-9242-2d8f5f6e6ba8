package net.summerfarm.manage.facade.pms;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.resp.PopBuyerQueryResp;
import net.summerfarm.manage.facade.pms.converter.PopSkuCostConvert;
import net.summerfarm.manage.facade.pms.dto.PopSkuCostFacadeDTO;
import net.summerfarm.pms.client.provider.pop.PopSkuAdditionQueryProvider;
import net.summerfarm.pms.client.req.pop.PopSkuCostQueryReq;
import net.summerfarm.pms.client.resp.dto.PopSkuCostDTO;
import net.summerfarm.pms.client.resp.pop.PopSkuCostQueryResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description POP sku 查询
 * @Date 2024/12/12 10:44
 * @<AUTHOR>
 */
@Component
@Slf4j
public class PopSkuAdditionQueryFacade {

    @DubboReference
    private PopSkuAdditionQueryProvider popSkuAdditionQueryProvider;


    public List<PopSkuCostFacadeDTO> queryPopSkuCost(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Lists.newArrayList();
        }
        try {
            PopSkuCostQueryReq queryReq = PopSkuCostQueryReq.builder()
                    .skuList(skuList).build();
            DubboResponse<PopSkuCostQueryResp> resp = popSkuAdditionQueryProvider.queryPopSkuCost(queryReq);
            if(!resp.isSuccess() || Objects.isNull(resp.getData())){
                log.error("调用pms查询sku成本异常 skuList:{} resp:{}", skuList, JSON.toJSONString(resp));
                throw new ProviderException("调用pms查询sku成本异常:" + resp.getMsg());
            }
            List<PopSkuCostDTO> popSkuCostDTOList = resp.getData().getPopSkuCostList();
            if (CollectionUtils.isEmpty(popSkuCostDTOList)) {
                return Lists.newArrayList();
            }
            return popSkuCostDTOList.stream().map(PopSkuCostConvert::convert).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("queryPopSkuCost skuList:{}", skuList, e);
            return Lists.newArrayList();
        }

    }

}
