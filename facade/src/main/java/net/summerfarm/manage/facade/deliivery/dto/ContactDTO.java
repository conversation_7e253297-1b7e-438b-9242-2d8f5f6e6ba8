package net.summerfarm.manage.facade.deliivery.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/11/14 16:00:20
 */
@Data
public class ContactDTO implements Serializable {

    /**
     * 地址ID
     */
    private Long contactId;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;

    /**
     * 周的配送周期 0每天 1周一 依次,多个逗号分隔
     */
    private String weekDeliveryFrequent;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;
}
