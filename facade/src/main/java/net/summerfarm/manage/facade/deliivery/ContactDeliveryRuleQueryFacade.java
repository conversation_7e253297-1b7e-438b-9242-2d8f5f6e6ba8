package net.summerfarm.manage.facade.deliivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.facade.deliivery.converter.ContactConverter;
import net.summerfarm.manage.facade.deliivery.dto.ContactDTO;
import net.summerfarm.manage.facade.deliivery.input.ContactDeliveryRuleQueryInput;
import net.summerfarm.wnc.client.provider.deliveryRule.ContactDeliveryRuleQueryProvider;
import net.summerfarm.wnc.client.req.deliveryRule.XmContactDeliveryRuleQueryReq;
import net.summerfarm.wnc.client.resp.deliveryRule.XmContactDeliveryRuleResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description 门店地址配送周期查询
 * @date 2023/11/14 10:20:24
 */
@Component
@Slf4j
public class ContactDeliveryRuleQueryFacade {

    @DubboReference
    private ContactDeliveryRuleQueryProvider contactDeliveryRuleQueryProvider;

    /**
     * @description: 获取门店地址配送周期
     * @author: lzh
     * @date: 2023/11/14 15:59
     * @param: [input]
     * @return: void
     **/
    public ContactDTO queryXmContactDeliveryRule(ContactDeliveryRuleQueryInput input) {
        if (input == null) {
            return null;
        }
        XmContactDeliveryRuleQueryReq ruleQueryReq = new XmContactDeliveryRuleQueryReq();
        ruleQueryReq.setOutBusinessNo(input.getOutBusinessNo());
        DubboResponse<XmContactDeliveryRuleResp> response = contactDeliveryRuleQueryProvider.queryXmContactDeliveryRule(ruleQueryReq);
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.error("ContactDeliveryRuleQueryFacade[]queryXmContactDeliveryRule[]error cause:{}", JSON.toJSONString(response));
            throw new BizException(response.getMsg());
        }
        return ContactConverter.toContactDTO(response.getData());
    }
}
