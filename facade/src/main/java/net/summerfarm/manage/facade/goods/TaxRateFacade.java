package net.summerfarm.manage.facade.goods;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.TaxRateProvider;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class TaxRateFacade {
    @DubboReference
    private TaxRateProvider taxRateProvider;

    public void copyTaxRate(Long pdId, Long newPdId) {
        DubboResponse<Void> resp = taxRateProvider.copy4Spu (pdId, newPdId);
        if (!resp.isSuccess()) {
            log.error("调用货品copyTaxRate异常 pdId:{} newPdId:{}", pdId,newPdId);
            throw new ProviderException("调用货品copyTaxRate异常:" + resp.getMsg());
        }
    }
}