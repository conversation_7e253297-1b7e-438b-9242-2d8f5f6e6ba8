package net.summerfarm.manage.facade.merchant.converter;

import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MerchantPageQueryConverter {


    private MerchantPageQueryConverter() {
        // 无需实现
    }

    public static List<MerchantStorePageQueryReq> toMerchantStorePageQueryReqList(List<MerchantQueryInput> merchantQueryInputList) {
        if (merchantQueryInputList == null) {
            return Collections.emptyList();
        }
        List<MerchantStorePageQueryReq> merchantStorePageQueryReqList = new ArrayList<>();
        for (MerchantQueryInput merchantQueryInput : merchantQueryInputList) {
            merchantStorePageQueryReqList.add(toMerchantStorePageQueryReq(merchantQueryInput));
        }
        return merchantStorePageQueryReqList;
    }

    public static MerchantStorePageQueryReq toMerchantStorePageQueryReq(MerchantQueryInput merchantQueryInput) {
        if (merchantQueryInput == null) {
            return null;
        }
        MerchantStorePageQueryReq merchantStorePageQueryReq = new MerchantStorePageQueryReq();
        merchantStorePageQueryReq.setStatus(merchantQueryInput.getStatus());
        merchantStorePageQueryReq.setPhone(merchantQueryInput.getPhone());
        merchantStorePageQueryReq.setProvince(merchantQueryInput.getProvince());
        merchantStorePageQueryReq.setCity(merchantQueryInput.getCity());
        merchantStorePageQueryReq.setArea(merchantQueryInput.getArea());
        merchantStorePageQueryReq.setMId(merchantQueryInput.getMId());
        merchantStorePageQueryReq.setAreaNo(merchantQueryInput.getAreaNo());
        merchantStorePageQueryReq.setDirect(merchantQueryInput.getDirect());
        merchantStorePageQueryReq.setMIds(merchantQueryInput.getMIds());
        merchantStorePageQueryReq.setAreaNos(merchantQueryInput.getAreaNos());
        merchantStorePageQueryReq.setPageIndex(merchantQueryInput.getPageIndex());
        merchantStorePageQueryReq.setPageSize(merchantQueryInput.getPageSize());
        merchantStorePageQueryReq.setSortList(merchantQueryInput.getSortList());
        merchantStorePageQueryReq.setStoreName(merchantQueryInput.getMname());
        merchantStorePageQueryReq.setSize(merchantQueryInput.getSize());
        merchantStorePageQueryReq.setType(merchantQueryInput.getType());
        merchantStorePageQueryReq.setStartTime(merchantQueryInput.getRegisterTime());
// Not mapped TO fields:
// id
// storeNo
// tenantId
// storeName
// type
// storeIds
// startTime
// endTime
// supplyStatus
// supplyStoreIds
// billSwitch
// noMatchingStoreIds
// groupId
// size
// Not mapped FROM fields:
// roleId
// mname
// mcontact
// openid
// rankId
// registerTime
// loginTime
// invitecode
// channelCode
// inviterChannelCode
// auditTime
// auditUser
// businessLicense
// address
// poiNote
// remark
// shopSign
// otherProof
// lastOrderTime
// size
// type
// tradeArea
// tradeGroup
// unionid
// mpOpenid
// adminId
// server
// popView
// memberIntegral
// grade
// skuShow
// rechargeAmount
// cashAmount
// cashUpdateTime
// showPrice
// mergeAdmin
// mergeTime
// firstLoginPop
// changePop
// pullBlackRemark
// pullBlackOperator
// houseNumber
// companyBrand
// cluePool
// merchantType
// enterpriseScale
// updateTime
// examineType
// displayButton
// operateStatus
// updater
// doorPic
// preRegisterFlag
// merchantLabel
// merchantLabelList
// recordId
        return merchantStorePageQueryReq;
    }

    public static List<MerchantQueryInput> toMerchantQueryInputList(List<MerchantStorePageQueryReq> merchantStorePageQueryReqList) {
        if (merchantStorePageQueryReqList == null) {
            return Collections.emptyList();
        }
        List<MerchantQueryInput> merchantQueryInputList = new ArrayList<>();
        for (MerchantStorePageQueryReq merchantStorePageQueryReq : merchantStorePageQueryReqList) {
            merchantQueryInputList.add(toMerchantQueryInput(merchantStorePageQueryReq));
        }
        return merchantQueryInputList;
    }

    public static MerchantQueryInput toMerchantQueryInput(MerchantStorePageQueryReq merchantStorePageQueryReq) {
        if (merchantStorePageQueryReq == null) {
            return null;
        }
        MerchantQueryInput merchantQueryInput = new MerchantQueryInput();
        merchantQueryInput.setMId(merchantStorePageQueryReq.getMId());
        merchantQueryInput.setPhone(merchantStorePageQueryReq.getPhone());
        merchantQueryInput.setStatus(merchantStorePageQueryReq.getStatus());
        merchantQueryInput.setProvince(merchantStorePageQueryReq.getProvince());
        merchantQueryInput.setCity(merchantStorePageQueryReq.getCity());
        merchantQueryInput.setArea(merchantStorePageQueryReq.getArea());
        merchantQueryInput.setAreaNo(merchantStorePageQueryReq.getAreaNo());
        merchantQueryInput.setDirect(merchantStorePageQueryReq.getDirect());
        merchantQueryInput.setMIds(merchantStorePageQueryReq.getMIds());
        merchantQueryInput.setAreaNos(merchantStorePageQueryReq.getAreaNos());
        merchantQueryInput.setPageIndex(merchantStorePageQueryReq.getPageIndex());
        merchantQueryInput.setPageSize(merchantStorePageQueryReq.getPageSize());
        merchantQueryInput.setSortList(merchantStorePageQueryReq.getSortList());
// Not mapped TO fields:
// roleId
// mname
// mcontact
// openid
// rankId
// registerTime
// loginTime
// invitecode
// channelCode
// inviterChannelCode
// auditTime
// auditUser
// businessLicense
// address
// poiNote
// remark
// shopSign
// otherProof
// lastOrderTime
// size
// type
// tradeArea
// tradeGroup
// unionid
// mpOpenid
// adminId
// server
// popView
// memberIntegral
// grade
// skuShow
// rechargeAmount
// cashAmount
// cashUpdateTime
// showPrice
// mergeAdmin
// mergeTime
// firstLoginPop
// changePop
// pullBlackRemark
// pullBlackOperator
// houseNumber
// companyBrand
// cluePool
// merchantType
// enterpriseScale
// updateTime
// examineType
// displayButton
// operateStatus
// updater
// doorPic
// preRegisterFlag
// merchantLabel
// merchantLabelList
// recordId
// Not mapped FROM fields:
// id
// storeNo
// tenantId
// storeName
// type
// storeIds
// startTime
// endTime
// supplyStatus
// supplyStoreIds
// billSwitch
// noMatchingStoreIds
// groupId
// size
        return merchantQueryInput;
    }
}
