package net.summerfarm.manage.facade.goods.converter;

import net.summerfarm.goods.client.dto.PopBuyerDTO;
import net.summerfarm.manage.facade.goods.dto.PopBuyerInfoDTO;

import java.util.Objects;

/**
 * @Description
 * @Date 2024/7/3 18:07
 * @<AUTHOR>
 */
public class PopBuyerConvert {

    public static PopBuyerInfoDTO convert(PopBuyerDTO popBuyerDTO) {
        if (Objects.isNull(popBuyerDTO)) {
            return null;
        }
        return PopBuyerInfoDTO.builder()
                .buyerId(popBuyerDTO.getBuyerId())
                .buyerName(popBuyerDTO.getBuyerName())
                .adminId(popBuyerDTO.getAdminId())
                .source(popBuyerDTO.getSource())
                .creator(popBuyerDTO.getCreator())
                .operator(popBuyerDTO.getOperator())
                .build();

    }
}
