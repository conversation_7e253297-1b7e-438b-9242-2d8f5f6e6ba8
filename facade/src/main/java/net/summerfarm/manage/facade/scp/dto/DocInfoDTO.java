package net.summerfarm.manage.facade.scp.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DocInfoDTO {
    /**
     * doc 指“Days of Coverage”，即库存覆盖天数
     */
    private BigDecimal doc;

    /**
     * SKU+仓维度的A,B,C分档标签
     * @see net.xianmu.scp.common.enums.SkuWarehouseSellLabelEnums.LevelLabel
     */
    private String levelLabel;

    /**
     * 最小安全库存天数
     */
    private BigDecimal stockLevelMinimumDay;

    /**
     * 目标库存天数
     */
    private BigDecimal stockLevelTargetDay;

    /**
     * 最大安全库存天数
     */
    private BigDecimal stockLevelMaximumDay;

    /**
     * 日销
     */
    private Integer salesQuantity;

    /**
     * 最小安全库存数量
     */
    private Integer stockLevelMinimum;

    /**
     * 最大安全库存数量
     */
    private Integer stockLevelMaximum;

    /**
     * 目标安全库存数量
     */
    private Long stockLevelTarget;

    /**
     * 期初库存
     */
    private BigDecimal initStockQuantity;
}
