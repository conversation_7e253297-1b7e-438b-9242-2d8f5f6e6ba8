package net.summerfarm.manage.facade.item;

import com.cofso.item.client.enums.DeleteFlagEnum;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.page.PageResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.security.ProviderException;

@Component
public class MarketItemFacade {

    private static final Long xmTenantId = 1L;

    @DubboReference
    private MarketItemProvider marketItemProvider;

    public PageResp<MarketItemInfoResp> queryMarketItemList(MarketItemCommonQueryReq param) {
        param.setTenantId(xmTenantId);
        param.setDeleteFlag(DeleteFlagEnum.NORMAL.getFlag());
        DubboResponse<PageResp<MarketItemInfoResp>> response = marketItemProvider.queryMarketItemList(param);

        if (response.isSuccess()) {
            return response.getData();
        }
        throw new ProviderException(response.getMsg());
    }
}
