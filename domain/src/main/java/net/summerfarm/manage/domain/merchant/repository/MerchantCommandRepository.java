package net.summerfarm.manage.domain.merchant.repository;

import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantCommandParam;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-09-19 10:55:24
* @version 1.0
*
*/
public interface MerchantCommandRepository {

    MerchantEntity insertSelective(MerchantEntity record);

    Boolean updateByPrimaryKeySelective(MerchantCommandParam merchantCommandParam);

    int updateByPrimaryKeySelectiveBatch(List<MerchantCommandParam> list);
}