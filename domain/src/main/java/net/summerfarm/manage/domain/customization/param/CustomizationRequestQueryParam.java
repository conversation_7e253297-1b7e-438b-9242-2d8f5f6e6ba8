package net.summerfarm.manage.domain.customization.param;

import lombok.Data;

@Data
public class CustomizationRequestQueryParam {
    /**
     * 查看详情时主键Id不能为空
     */
    private Long id;

    /**
     * 客户名称
     */
    private String storeName;

    /**
     * 主订单号
     */
    private String masterOrderNo;


    /**
     * 状态，0-定制需求待生成(订单未支付),1-待设计师设计(订单已支付),2-设计师发起确认,3-客户通过,4-客户不通过,5-关闭(订单取消/退款)
     */
    private Integer status;
}
