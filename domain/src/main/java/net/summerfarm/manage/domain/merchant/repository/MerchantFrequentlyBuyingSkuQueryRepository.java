package net.summerfarm.manage.domain.merchant.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuQueryParam;



/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
public interface MerchantFrequentlyBuyingSkuQueryRepository {

    PageInfo<MerchantFrequentlyBuyingSkuEntity> getPage(MerchantFrequentlyBuyingSkuQueryParam param);

    MerchantFrequentlyBuyingSkuEntity selectById(Long id);

    List<MerchantFrequentlyBuyingSkuEntity> selectByCondition(MerchantFrequentlyBuyingSkuQueryParam param);

}