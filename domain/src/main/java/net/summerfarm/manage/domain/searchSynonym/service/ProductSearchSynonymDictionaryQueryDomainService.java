



import net.summerfarm.manage.domain.searchSynonym.repository.ProductSearchSynonymDictionaryQueryRepository;
import net.summerfarm.manage.domain.searchSynonym.repository.ProductSearchSynonymDictionaryCommandRepository;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: ES用到的同义词表，每一行是一组同义词，比如："葡萄,巨峰,巨峰葡萄,夏黑,夏黑葡萄,青提,晴王青提,阳光玫瑰"领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-04-24 14:53:58
 * @version 1.0
 *
 */
@Service
public class ProductSearchSynonymDictionaryQueryDomainService {


}
