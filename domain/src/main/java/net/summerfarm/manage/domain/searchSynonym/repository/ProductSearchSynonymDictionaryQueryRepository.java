package net.summerfarm.manage.domain.searchSynonym.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.domain.searchSynonym.param.query.ProductSearchSynonymDictionaryQueryParam;



/**
*
* <AUTHOR>
* @date 2025-04-24 14:53:58
* @version 1.0
*
*/
public interface ProductSearchSynonymDictionaryQueryRepository {

    PageInfo<ProductSearchSynonymDictionaryEntity> getPage(ProductSearchSynonymDictionaryQueryParam param);

    ProductSearchSynonymDictionaryEntity selectById(Long id);

    List<ProductSearchSynonymDictionaryEntity> selectByCondition(ProductSearchSynonymDictionaryQueryParam param);

}