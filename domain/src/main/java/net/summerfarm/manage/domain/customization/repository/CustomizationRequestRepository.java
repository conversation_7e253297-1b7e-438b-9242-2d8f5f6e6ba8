package net.summerfarm.manage.domain.customization.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.customization.entity.CustomizationRequestEntity;

import java.util.List;
import net.summerfarm.manage.domain.customization.param.CustomizationRequestQueryParam;

/**
 * 定制需求仓储接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface CustomizationRequestRepository {

    /**
     * 保存定制需求
     * 
     * @param entity 定制需求实体
     * @return 保存后的实体
     */
    CustomizationRequestEntity save(CustomizationRequestEntity entity);

    /**
     * 更新定制需求
     * 
     * @param entity 定制需求实体
     * @return 更新后的实体
     */
    CustomizationRequestEntity update(CustomizationRequestEntity entity);
    void updateStatusByMasterOrderNo(Integer byOrdertatus, String orderNo);

    /**
     * 根据ID查询定制需求
     * 
     * @param id 主键ID
     * @return 定制需求实体
     */
    CustomizationRequestEntity findById(Long id);

    /**
     * 分页查询定制需求列表
     * 
     * @param entity 查询条件
     * @return 定制需求列表
     */
    List<CustomizationRequestEntity> findList(CustomizationRequestQueryParam entity);
    PageInfo<CustomizationRequestEntity> page(CustomizationRequestQueryParam param, Integer pageIndex, Integer pageSize);

    /**
     * 根据主订单号查询定制需求
     * @param masterOrderNo
     * @return
     */
    CustomizationRequestEntity queryByMasterOrderNo(String masterOrderNo);

}
