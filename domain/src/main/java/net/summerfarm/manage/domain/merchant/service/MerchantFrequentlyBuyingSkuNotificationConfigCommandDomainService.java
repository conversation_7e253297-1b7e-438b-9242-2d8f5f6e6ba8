package net.summerfarm.manage.domain.merchant.service;


import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuNotificationConfigQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuNotificationConfigCommandRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantFrequentlyBuyingSkuNotificationConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 门店常购清单配置领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Service
public class MerchantFrequentlyBuyingSkuNotificationConfigCommandDomainService {


    @Autowired
    private MerchantFrequentlyBuyingSkuNotificationConfigCommandRepository merchantFrequentlyBuyingSkuNotificationConfigCommandRepository;
    @Autowired
    private MerchantFrequentlyBuyingSkuNotificationConfigQueryRepository merchantFrequentlyBuyingSkuNotificationConfigQueryRepository;



    public MerchantFrequentlyBuyingSkuNotificationConfigEntity insert(MerchantFrequentlyBuyingSkuNotificationConfigCommandParam param) {
        return merchantFrequentlyBuyingSkuNotificationConfigCommandRepository.insertSelective(param);
    }


    public int update(MerchantFrequentlyBuyingSkuNotificationConfigCommandParam param) {
        return merchantFrequentlyBuyingSkuNotificationConfigCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return merchantFrequentlyBuyingSkuNotificationConfigCommandRepository.remove(id);
    }
}
