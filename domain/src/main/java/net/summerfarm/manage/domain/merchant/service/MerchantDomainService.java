package net.summerfarm.manage.domain.merchant.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.RandomCodeUtils;
import net.summerfarm.manage.common.enums.MerchantEnums;
import net.summerfarm.manage.common.util.PoiSearchUtil;
import net.summerfarm.manage.common.util.ProvinceConverterV2;
import net.summerfarm.manage.common.util.ProvinceConveter;
import net.summerfarm.manage.common.util.ValidateUtil;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantSubAccountEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantCommandParam;
import net.summerfarm.manage.domain.merchant.param.query.MerchantQueryParam;
import net.summerfarm.manage.domain.merchant.repository.MerchantCommandRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantSubAccountQueryRepository;
import net.summerfarm.manage.facade.wnc.FenceFacade;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Title: 商户表业务逻辑实现类
 * @Description:
 * @date 2023-09-19 10:55:24
 */

@Slf4j
@Service
public class MerchantDomainService {

    @Autowired
    private MerchantQueryRepository merchantQueryRepository;
    @Autowired
    private MerchantCommandRepository merchantCommandRepository;
    @Autowired
    private MerchantSubAccountQueryRepository accountQueryRepository;

    @Autowired
    private AccountDomainService accountDomainService;
    @Autowired
    private ContactDomainService contactDomainService;
    @Autowired
    private FenceFacade fenceFacade;
    @Resource
    private ProvinceConverterV2 provinceConverterV2;


    public MerchantEntity addMerchant(MerchantEntity reqEntity) {

        // 1.预检查
        this.preValidate(reqEntity);

        // 2.数据补充
        this.warpMerchantEntity(reqEntity);
        MerchantEntity entity = merchantCommandRepository.insertSelective(reqEntity);
        reqEntity.setMId(entity.getMId());

        // 3.子账户
        accountDomainService.addAccount(entity);

        // 4.创建地址
        if (!reqEntity.isBatchImport()) {
            contactDomainService.addContact(reqEntity);
        } else {
            // 批量导入时，创建非默认地址，并根据城市+区域获取城配仓和poi
            contactDomainService.addContactForMerchantBatchCreate(reqEntity);
        }
        log.info("创建门店成功，id:{}", entity.getMId());
        return entity;
    }


    private void preValidate(MerchantEntity entity) {
        log.info("领域层接收到创建门店请求,data:{}", JSON.toJSONString(entity));
        // 参数校验
        String phone = entity.getPhone();
        String name = entity.getMname();
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(name)) {
            throw new BizException("该店铺名或者手机号为空");
        }

        // 门店名称是否合法
        if(!ValidateUtil.isStoreName(name)) {
            log.error("长度超过限制or存在非法字符!name:{}", name);
            throw new BizException("该店铺名含有非法字符");
        }

        // 数据库校验
        List<MerchantEntity> entities = merchantQueryRepository.selectByName(name);
        if (CollUtil.isNotEmpty(entities)) {
            throw new BizException("该店铺名已经注册");
        }
        MerchantQueryParam merchantQueryParam = new MerchantQueryParam();
        merchantQueryParam.setOpenid(phone);
        MerchantEntity merchantEntity = merchantQueryRepository.selectByParam(merchantQueryParam);
        if (merchantEntity != null){
            throw new BizException("当前手机号已注册，对应门店mid:"+merchantEntity.getMId());
        }
        // 门店查询
        Map selectKey = new HashMap();
        selectKey.put("phone", phone);
        List<MerchantSubAccountEntity> accountEntities = accountQueryRepository.selectListByPhone(selectKey);
        if (CollUtil.isNotEmpty(accountEntities)) {
            log.info("手机号：{}已被注册，mid：{}", phone, JSON.toJSONString(accountEntities.stream().map(MerchantSubAccountEntity::getMId).collect(Collectors.toList())));
            throw new BizException("该手机号已经注册");
        }
    }

    private void warpMerchantEntity(MerchantEntity entity){
        // 初始化
        entity.setOpenid(entity.getPhone());
        entity.setRegisterTime(LocalDateTime.now());
        entity.setProvince(ProvinceConveter.convert(entity.getProvince()));
        entity.setChannelCode(generateChannelCode());

        // 补充运营区域
        if(entity.isInitContact()){
            Integer areaNo = fenceFacade.getXmAreaByAddress(entity.getCity(), entity.getArea());
            if(areaNo == null) {
                log.error("门店地址不在配送范围,city:{}, area:{}", entity.getCity(), entity.getArea());
                throw new BizException("地址不在配送范围");
            }
            entity.setAreaNo(areaNo);

            // 省份数据清洗
            entity.setProvince(provinceConverterV2.convert(entity.getProvince()));

            //补充poi，并模拟自动审核 （这里应产品要求，获取不到poi时，不抛出异常，回滚事务。让业务走地址审核的逻辑补充poi）
            String address = StringUtils.defaultString(entity.getProvince()) +
                    StringUtils.defaultString(entity.getCity()) +
                    StringUtils.defaultString(entity.getArea()) +
                    StringUtils.defaultString(entity.getAddress());
            String poiNote = PoiSearchUtil.getPoiByAddress(address);
            log.info("获取poi结果：{}, {}", JSON.toJSONString(entity), poiNote);
            if(StringUtils.isNotBlank(poiNote)) {
                entity.setPoiNote(poiNote);
            }
        }
    }

    /**
     * 生成推荐码，这里的生成规则隐患很大
     * @return
     */
    private String generateChannelCode(){
        boolean repeat = true;
        String channelCode = null;
        while (repeat) {
            channelCode = RandomCodeUtils.toSerialNumber(6);
            repeat = merchantQueryRepository.existChannelCode(channelCode);
        }
        log.info("商户推荐码：{}", channelCode);
        return channelCode;
    }

    /***
     * @author: lzh
     * @description: 确认收货更新用户的积分
     * @date: 2024/1/18 18:37
     * @param: [merchantCommandParam]
     * @return: void
     **/
    public Boolean updateMemberScore(MerchantCommandParam merchantCommandParam) {
        return merchantCommandRepository.updateByPrimaryKeySelective(merchantCommandParam);
    }
}
