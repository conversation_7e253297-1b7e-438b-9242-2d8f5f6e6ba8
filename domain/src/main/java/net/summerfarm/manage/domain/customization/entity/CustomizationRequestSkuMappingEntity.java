package net.summerfarm.manage.domain.customization.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 定制需求sku关联领域实体
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class CustomizationRequestSkuMappingEntity {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 定制需求id
     */
    private Long customizationRequestId;

    /**
     * sku
     */
    private String sku;

    /**
     * 模版sku
     */
    private String sourceSku;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
