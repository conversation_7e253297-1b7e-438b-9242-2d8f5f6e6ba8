package net.summerfarm.manage.domain.merchant.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.merchant.entity.MerchantSkuOrderDataEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantSkuOrderDataQueryParam;



/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
public interface MerchantSkuOrderDataQueryRepository {

    PageInfo<MerchantSkuOrderDataEntity> getPage(MerchantSkuOrderDataQueryParam param);

    MerchantSkuOrderDataEntity selectById(Long id);

    List<MerchantSkuOrderDataEntity> selectByCondition(MerchantSkuOrderDataQueryParam param);

}