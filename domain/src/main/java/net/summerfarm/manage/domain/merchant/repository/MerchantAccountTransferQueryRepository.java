package net.summerfarm.manage.domain.merchant.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantAccountTransferQueryParam;



/**
*
* <AUTHOR>
* @date 2024-01-10 14:07:22
* @version 1.0
*
*/
public interface MerchantAccountTransferQueryRepository {

    PageInfo<MerchantAccountTransferEntity> getPage(MerchantAccountTransferQueryParam param);

    MerchantAccountTransferEntity selectById(Long id);

}