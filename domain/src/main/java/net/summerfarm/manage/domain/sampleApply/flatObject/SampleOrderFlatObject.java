package net.summerfarm.manage.domain.sampleApply.flatObject;

import lombok.Data;

import java.time.LocalDate;

/**
 * Description: 样品单<br/>
 * date: 2025/1/2 14:05<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SampleOrderFlatObject {

    /**
     * ID
     */
    private Long sampleId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 1大客户\2大连锁3\小连锁\4单点
     */
    private String msize;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人地址
     */
    private String contactAddress;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 规格
     */
    private String weight;

    /**
     * sku
     */
    private String sku;

    /**
     * 联系人ID
     */
    private Integer contactId;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 鲜沐大客户ID
     */
    private Long bigCustomerId;

}
